-- =====================================================
-- OTHER FEES MANAGEMENT TABLES
-- =====================================================

-- Create enum for other fee types
DO $$ BEGIN
    CREATE TYPE fee_type AS ENUM ('fixed', 'percentage');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Other Fees table
CREATE TABLE IF NOT EXISTS public.other_fees (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  amount DECIMAL(15,2) NOT NULL DEFAULT 0,
  type fee_type NOT NULL DEFAULT 'fixed',
  is_active BOOLEAN DEFAULT true,
  created_by UUID REFERENCES public.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Order Other Fees table (junction table for orders and other fees)
CREATE TABLE IF NOT EXISTS public.order_other_fees (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  order_id UUID REFERENCES public.orders(id) ON DELETE CASCADE,
  other_fee_id UUID REFERENCES public.other_fees(id),
  name VARCHAR(255) NOT NULL, -- Store name at time of order
  amount DECIMAL(15,2) NOT NULL, -- Store original amount
  type fee_type NOT NULL, -- Store type at time of order
  calculated_amount DECIMAL(15,2) NOT NULL, -- Actual calculated amount
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- INDEXES
-- =====================================================

-- Other fees indexes
CREATE INDEX IF NOT EXISTS idx_other_fees_active ON public.other_fees(is_active);
CREATE INDEX IF NOT EXISTS idx_other_fees_name ON public.other_fees(name);
CREATE INDEX IF NOT EXISTS idx_other_fees_type ON public.other_fees(type);
CREATE INDEX IF NOT EXISTS idx_other_fees_created_by ON public.other_fees(created_by);

-- Order other fees indexes
CREATE INDEX IF NOT EXISTS idx_order_other_fees_order_id ON public.order_other_fees(order_id);
CREATE INDEX IF NOT EXISTS idx_order_other_fees_fee_id ON public.order_other_fees(other_fee_id);

-- =====================================================
-- ROW LEVEL SECURITY (RLS)
-- =====================================================

-- Enable RLS
ALTER TABLE public.other_fees ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.order_other_fees ENABLE ROW LEVEL SECURITY;

-- Other fees policies
CREATE POLICY "Users can view other fees" ON public.other_fees
  FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "Users can insert other fees" ON public.other_fees
  FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

CREATE POLICY "Users can update their other fees" ON public.other_fees
  FOR UPDATE USING (auth.uid() IS NOT NULL);

CREATE POLICY "Users can delete their other fees" ON public.other_fees
  FOR DELETE USING (auth.uid() IS NOT NULL);

-- Order other fees policies
CREATE POLICY "Users can view order other fees" ON public.order_other_fees
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.orders 
      WHERE orders.id = order_other_fees.order_id 
      AND orders.created_by = auth.uid()
    )
  );

CREATE POLICY "Users can insert order other fees" ON public.order_other_fees
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.orders 
      WHERE orders.id = order_other_fees.order_id 
      AND orders.created_by = auth.uid()
    )
  );

-- =====================================================
-- TRIGGERS
-- =====================================================

-- Update timestamp trigger for other_fees
CREATE OR REPLACE FUNCTION update_other_fees_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_other_fees_updated_at
  BEFORE UPDATE ON public.other_fees
  FOR EACH ROW
  EXECUTE FUNCTION update_other_fees_updated_at();

-- =====================================================
-- SAMPLE DATA
-- =====================================================

-- Insert sample other fees
INSERT INTO public.other_fees (name, description, amount, type, is_active) VALUES
('Phí giao hàng', 'Phí giao hàng tận nơi', 20000, 'fixed', true),
('Phí dịch vụ', 'Phí dịch vụ 5%', 5, 'percentage', true),
('Phí đóng gói', 'Phí đóng gói sản phẩm', 5000, 'fixed', true),
('Thuế VAT', 'Thuế giá trị gia tăng 10%', 10, 'percentage', true)
ON CONFLICT DO NOTHING;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Check if tables were created
SELECT 'other_fees' as table_name, COUNT(*) as record_count 
FROM public.other_fees
UNION ALL
SELECT 'order_other_fees' as table_name, COUNT(*) as record_count 
FROM public.order_other_fees;

-- Check enum types
SELECT unnest(enum_range(NULL::fee_type)) as fee_type;

-- Check indexes
SELECT indexname, tablename 
FROM pg_indexes 
WHERE tablename IN ('other_fees', 'order_other_fees')
  AND schemaname = 'public'
ORDER BY tablename, indexname;
