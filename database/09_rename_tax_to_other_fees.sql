-- =====================================================
-- CITY POS - RENAME TAX_AMOUNT TO OTHER_FEES_AMOUNT IN INVOICES TABLE
-- =====================================================
-- This migration renames the tax_amount column to other_fees_amount
-- to better reflect its current usage for storing other fees instead of tax

-- =====================================================
-- RENAME COLUMN IN INVOICES TABLE
-- =====================================================

-- Rename tax_amount column to other_fees_amount
ALTER TABLE public.invoices 
RENAME COLUMN tax_amount TO other_fees_amount;

-- Add comment to clarify the column purpose
COMMENT ON COLUMN public.invoices.other_fees_amount IS 'Total amount of other fees (previously tax_amount)';

-- =====================================================
-- UPDATE EXISTING VIEWS IF ANY
-- =====================================================

-- If there are any views that reference tax_amount, they would need to be updated
-- Currently no views are using this column, so no updates needed

-- =====================================================
-- VERIFICATION
-- =====================================================

-- Check that the column was renamed successfully
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
  AND table_name = 'invoices' 
  AND column_name IN ('tax_amount', 'other_fees_amount');

-- Show sample data to verify
SELECT 
  id,
  invoice_number,
  subtotal,
  other_fees_amount,
  discount_amount,
  total_amount
FROM public.invoices 
LIMIT 5;
