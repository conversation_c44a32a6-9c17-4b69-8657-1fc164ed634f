class OtherFee {
  final String id;
  final String name;
  final String description;
  final double amount;
  final String type; // 'fixed' or 'percentage'
  final bool isActive;
  final String? createdBy;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const OtherFee({
    required this.id,
    required this.name,
    required this.description,
    required this.amount,
    this.type = 'fixed',
    this.isActive = true,
    this.createdBy,
    this.createdAt,
    this.updatedAt,
  });

  // Factory constructor from JSON
  factory OtherFee.fromJson(Map<String, dynamic> json) {
    return OtherFee(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String? ?? '',
      amount: (json['amount'] as num?)?.toDouble() ?? 0.0,
      type: json['type'] as String? ?? 'fixed',
      isActive: json['is_active'] as bool? ?? true,
      createdBy: json['created_by'] as String?,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'] as String)
          : null,
    );
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'amount': amount,
      'type': type,
      'is_active': isActive,
      'created_by': createdBy,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  // Convert to Supabase format
  Map<String, dynamic> toSupabase() {
    return {
      'name': name,
      'description': description,
      'amount': amount,
      'type': type,
      'is_active': isActive,
      'created_by': createdBy,
      'created_at': createdAt?.toIso8601String() ?? DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    };
  }

  // Copy with method
  OtherFee copyWith({
    String? id,
    String? name,
    String? description,
    double? amount,
    String? type,
    bool? isActive,
    String? createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return OtherFee(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      amount: amount ?? this.amount,
      type: type ?? this.type,
      isActive: isActive ?? this.isActive,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Calculate fee amount based on type
  double calculateFee(double subtotal) {
    if (type == 'percentage') {
      return subtotal * (amount / 100);
    }
    return amount; // fixed amount
  }

  // Equality and hashCode
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is OtherFee && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'OtherFee(id: $id, name: $name, amount: $amount, type: $type)';
  }
}

// Model for selected other fees in an order
class OrderOtherFee {
  final String id;
  final String orderId;
  final String otherFeeId;
  final String name;
  final double amount;
  final String type;
  final double calculatedAmount;
  final DateTime? createdAt;

  const OrderOtherFee({
    required this.id,
    required this.orderId,
    required this.otherFeeId,
    required this.name,
    required this.amount,
    required this.type,
    required this.calculatedAmount,
    this.createdAt,
  });

  // Factory constructor from JSON
  factory OrderOtherFee.fromJson(Map<String, dynamic> json) {
    return OrderOtherFee(
      id: json['id'] as String,
      orderId: json['order_id'] as String,
      otherFeeId: json['other_fee_id'] as String,
      name: json['name'] as String,
      amount: (json['amount'] as num?)?.toDouble() ?? 0.0,
      type: json['type'] as String,
      calculatedAmount: (json['calculated_amount'] as num?)?.toDouble() ?? 0.0,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : null,
    );
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'order_id': orderId,
      'other_fee_id': otherFeeId,
      'name': name,
      'amount': amount,
      'type': type,
      'calculated_amount': calculatedAmount,
      'created_at': createdAt?.toIso8601String(),
    };
  }

  // Convert to Supabase format
  Map<String, dynamic> toSupabase() {
    return {
      'order_id': orderId,
      'other_fee_id': otherFeeId,
      'name': name,
      'amount': amount,
      'type': type,
      'calculated_amount': calculatedAmount,
      'created_at': createdAt?.toIso8601String() ?? DateTime.now().toIso8601String(),
    };
  }

  // Factory constructor from OtherFee
  factory OrderOtherFee.fromOtherFee({
    required String orderId,
    required OtherFee otherFee,
    required double subtotal,
  }) {
    final calculatedAmount = otherFee.calculateFee(subtotal);
    return OrderOtherFee(
      id: '${DateTime.now().millisecondsSinceEpoch}',
      orderId: orderId,
      otherFeeId: otherFee.id,
      name: otherFee.name,
      amount: otherFee.amount,
      type: otherFee.type,
      calculatedAmount: calculatedAmount,
      createdAt: DateTime.now(),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is OrderOtherFee && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'OrderOtherFee(id: $id, name: $name, calculatedAmount: $calculatedAmount)';
  }
}
