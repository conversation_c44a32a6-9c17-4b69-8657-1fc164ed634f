import 'order_item.dart';
import 'other_fee.dart';
import 'partner.dart';

class Order {
  final String? id;
  final String? orderNumber;
  final String type; // sale, purchase, return
  final String status; // pending, confirmed, completed, cancelled
  final String? partnerId;
  final Partner? partner;
  final double subtotal;
  final double taxAmount;
  final double discountAmount;
  final double totalAmount;
  final String paymentStatus; // pending, partial, paid, refunded
  final String? paymentMethod;
  final String? notes;
  final List<OrderItem> items;
  final List<OrderOtherFee> otherFees;
  final String? createdBy;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const Order({
    this.id,
    this.orderNumber,
    this.type = 'sale',
    this.status = 'pending',
    this.partnerId,
    this.partner,
    this.subtotal = 0.0,
    this.taxAmount = 0.0,
    this.discountAmount = 0.0,
    this.totalAmount = 0.0,
    this.paymentStatus = 'pending',
    this.paymentMethod,
    this.notes,
    this.items = const [],
    this.otherFees = const [],
    this.createdBy,
    this.createdAt,
    this.updatedAt,
  });

  factory Order.fromJson(Map<String, dynamic> json) {
    return Order(
      id: json['id'] as String?,
      orderNumber: json['order_number'] as String?,
      type: json['type'] as String? ?? 'sale',
      status: json['status'] as String? ?? 'pending',
      partnerId: json['partner_id'] as String?,
      partner: json['partner'] != null
          ? Partner.fromJson(json['partner'])
          : null,
      subtotal: (json['subtotal'] as num?)?.toDouble() ?? 0.0,
      taxAmount: (json['tax_amount'] as num?)?.toDouble() ?? 0.0,
      discountAmount: (json['discount_amount'] as num?)?.toDouble() ?? 0.0,
      totalAmount: (json['total_amount'] as num?)?.toDouble() ?? 0.0,
      paymentStatus: json['payment_status'] as String? ?? 'pending',
      paymentMethod: json['payment_method'] as String?,
      notes: json['notes'] as String?,
      items: json['items'] != null
          ? (json['items'] as List)
                .map((item) => OrderItem.fromJson(item))
                .toList()
          : [],
      otherFees: json['other_fees'] != null
          ? (json['other_fees'] as List)
                .map((fee) => OrderOtherFee.fromJson(fee))
                .toList()
          : [],
      createdBy: json['created_by'] as String?,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'])
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      if (orderNumber != null) 'order_number': orderNumber,
      'type': type,
      'status': status,
      if (partnerId != null) 'partner_id': partnerId,
      'subtotal': subtotal,
      'tax_amount': taxAmount,
      'discount_amount': discountAmount,
      'total_amount': totalAmount,
      'payment_status': paymentStatus,
      if (paymentMethod != null) 'payment_method': paymentMethod,
      if (notes != null) 'notes': notes,
      if (createdBy != null) 'created_by': createdBy,
      if (createdAt != null) 'created_at': createdAt!.toIso8601String(),
      if (updatedAt != null) 'updated_at': updatedAt!.toIso8601String(),
    };
  }

  Order copyWith({
    String? id,
    String? orderNumber,
    String? type,
    String? status,
    String? partnerId,
    Partner? partner,
    double? subtotal,
    double? taxAmount,
    double? discountAmount,
    double? totalAmount,
    String? paymentStatus,
    String? paymentMethod,
    String? notes,
    List<OrderItem>? items,
    List<OrderOtherFee>? otherFees,
    String? createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Order(
      id: id ?? this.id,
      orderNumber: orderNumber ?? this.orderNumber,
      type: type ?? this.type,
      status: status ?? this.status,
      partnerId: partnerId ?? this.partnerId,
      partner: partner ?? this.partner,
      subtotal: subtotal ?? this.subtotal,
      taxAmount: taxAmount ?? this.taxAmount,
      discountAmount: discountAmount ?? this.discountAmount,
      totalAmount: totalAmount ?? this.totalAmount,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      notes: notes ?? this.notes,
      items: items ?? this.items,
      otherFees: otherFees ?? this.otherFees,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Equality and hashCode
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Order && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Order(id: $id, orderNumber: $orderNumber, status: $status, total: $totalAmount)';
  }
}

// Extension for Order
extension OrderExtension on Order {
  // Get display order number
  String get displayOrderNumber => orderNumber ?? 'N/A';

  // Get status display text
  String get statusDisplayText {
    switch (status) {
      case 'pending':
        return 'Chờ xử lý';
      case 'confirmed':
        return 'Đã xác nhận';
      case 'completed':
        return 'Hoàn thành';
      case 'cancelled':
        return 'Đã hủy';
      default:
        return status;
    }
  }

  // Get payment status display text
  String get paymentStatusDisplayText {
    switch (paymentStatus) {
      case 'pending':
        return 'Chờ thanh toán';
      case 'partial':
        return 'Thanh toán một phần';
      case 'paid':
        return 'Đã thanh toán';
      case 'refunded':
        return 'Đã hoàn tiền';
      default:
        return paymentStatus;
    }
  }

  // Get formatted total
  String get formattedTotal => '${totalAmount.toStringAsFixed(0)}đ';

  // Get formatted subtotal
  String get formattedSubtotal => '${subtotal.toStringAsFixed(0)}đ';

  // Get customer name
  String get customerName => partner?.name ?? 'Khách lẻ';

  // Get total items count
  int get totalItemsCount => items.fold(0, (sum, item) => sum + item.quantity);

  // Check if order is editable
  bool get isEditable => status == 'pending';

  // Check if order is cancellable
  bool get isCancellable => status == 'pending' || status == 'confirmed';

  // Check if order can be completed
  bool get canBeCompleted => status == 'confirmed';

  // Check if payment can be updated
  bool get canUpdatePayment => status != 'cancelled' && paymentStatus != 'paid';

  // Get formatted tax
  String get formattedTax => '${taxAmount.toStringAsFixed(0)}đ';

  // Get formatted discount
  String get formattedDiscount => '${discountAmount.toStringAsFixed(0)}đ';

  // Get status color
  String get statusColor {
    switch (status) {
      case 'pending':
        return '#F59E0B'; // Orange
      case 'confirmed':
        return '#3B82F6'; // Blue
      case 'completed':
        return '#10B981'; // Green
      case 'cancelled':
        return '#EF4444'; // Red
      default:
        return '#6B7280'; // Gray
    }
  }

  // Get payment status color
  String get paymentStatusColor {
    switch (paymentStatus) {
      case 'pending':
        return '#F59E0B'; // Orange
      case 'partial':
        return '#8B5CF6'; // Purple
      case 'paid':
        return '#10B981'; // Green
      case 'refunded':
        return '#EF4444'; // Red
      default:
        return '#6B7280'; // Gray
    }
  }

  // Check if order can be edited
  bool get canEdit => status == 'pending';

  // Check if order can be cancelled
  bool get canCancel => status == 'pending' || status == 'confirmed';

  // Check if order can be completed
  bool get canComplete => status == 'confirmed';

  // Get partner name
  String get partnerName => partner?.name ?? 'Khách lẻ';

  // Calculate total from items
  double get calculatedTotal {
    final itemsTotal = items.fold(0.0, (sum, item) => sum + item.totalAmount);
    final otherFeesTotal = otherFees.fold(
      0.0,
      (sum, fee) => sum + fee.calculatedAmount,
    );
    return itemsTotal + taxAmount + otherFeesTotal - discountAmount;
  }

  // Get total other fees amount
  double get totalOtherFeesAmount {
    return otherFees.fold(0.0, (sum, fee) => sum + fee.calculatedAmount);
  }

  // Get formatted other fees total
  String get formattedOtherFeesTotal =>
      '${totalOtherFeesAmount.toStringAsFixed(0)}đ';

  // Check if order is valid
  bool get isValid => items.isNotEmpty && totalAmount > 0;

  // Create copy for editing
  Order copyForEdit() {
    return copyWith(
      id: null,
      orderNumber: null,
      createdBy: null,
      createdAt: null,
      updatedAt: null,
    );
  }

  // Create copy with new status
  Order copyWithStatus(String newStatus) {
    return copyWith(status: newStatus);
  }

  // Create copy with new payment status
  Order copyWithPaymentStatus(String newPaymentStatus) {
    return copyWith(paymentStatus: newPaymentStatus);
  }
}
