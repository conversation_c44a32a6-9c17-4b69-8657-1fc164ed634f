import 'package:supabase_flutter/supabase_flutter.dart';

import '../../data/models/order.dart';
import '../config/supabase_config.dart';

class InvoiceService {
  static final SupabaseClient _supabase = SupabaseConfig.client;

  // Create invoice from order
  static Future<void> createInvoiceFromOrder(Order order) async {
    try {
      print('🧾 Bắt đầu tạo hóa đơn từ đơn hàng ${order.orderNumber}');

      // Calculate other fees total
      final otherFeesTotal = order.otherFees.fold(
        0.0,
        (sum, fee) => sum + fee.calculatedAmount,
      );

      // Real Supabase implementation
      final invoiceData = {
        'invoice_number':
            'INV${order.orderNumber?.substring(2) ?? DateTime.now().millisecondsSinceEpoch}',
        'order_id': order.id,
        'issue_date': DateTime.now().toIso8601String(),
        'subtotal': order.subtotal,
        'tax_amount': order.taxAmount,
        'discount_amount': order.discountAmount,
        'other_fees_amount': otherFeesTotal, // Include other fees
        'total_amount': order.totalAmount,
        'paid_amount':
            order.totalAmount, // Fully paid since it's from completed order
        'status': 'paid',
        'notes': order.otherFees.isNotEmpty
            ? 'Thu khác: ${order.otherFees.map((f) => '${f.name} (${f.calculatedAmount}₫)').join(', ')}'
            : null,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      print('🧾 Dữ liệu hóa đơn: $invoiceData');

      final response = await _supabase
          .from('invoices')
          .insert(invoiceData)
          .select();

      print('✅ Đã tạo hóa đơn thành công: ${response.first['invoice_number']}');
    } catch (e) {
      print('❌ Lỗi tạo hóa đơn: $e');
      print('❌ Chi tiết lỗi: ${e.toString()}');
      throw Exception('Lỗi tạo hóa đơn: $e');
    }
  }
}
