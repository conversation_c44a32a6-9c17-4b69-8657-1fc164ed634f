import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

// Printer imports
import '../../../core/providers/printer_provider.dart';
import '../../../core/services/pdf_service.dart';
import '../../../core/themes/app_theme.dart';
import '../../../data/models/order.dart';

class InvoiceDisplayScreen extends ConsumerStatefulWidget {
  final Order order;

  const InvoiceDisplayScreen({super.key, required this.order});

  @override
  ConsumerState<InvoiceDisplayScreen> createState() =>
      _InvoiceDisplayScreenState();
}

class _InvoiceDisplayScreenState extends ConsumerState<InvoiceDisplayScreen> {
  bool _isGeneratingPdf = false;
  bool _isPrinting = false;

  Future<void> _shareInvoice() async {
    setState(() {
      _isGeneratingPdf = true;
    });

    try {
      final pdfService = PDFService();
      await pdfService.generateAndShareInvoice(widget.order);

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white),
                SizedBox(width: 8),
                Text('Hóa đơn đã được chia sẻ thành công'),
              ],
            ),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );

        // Navigate back to POS after successful share
        Future.delayed(const Duration(seconds: 1), () {
          if (mounted) {
            context.go('/pos');
          }
        });
      }
    } catch (e) {
      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(child: Text('Lỗi khi chia sẻ hóa đơn: $e')),
              ],
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isGeneratingPdf = false;
        });
      }
    }
  }

  Future<void> _printInvoice() async {
    setState(() {
      _isPrinting = true;
    });

    try {
      // Check if there's a default printer
      final defaultPrinter = ref.read(defaultPrinterProvider);
      if (defaultPrinter == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Row(
                children: [
                  Icon(Icons.warning, color: Colors.white),
                  SizedBox(width: 8),
                  Text(
                    'Chưa có máy in nào được thiết lập. Vui lòng thiết lập máy in trong Settings.',
                  ),
                ],
              ),
              backgroundColor: Colors.orange,
              duration: Duration(seconds: 4),
            ),
          );
        }
        return;
      }

      // For now, show success message (actual printing will be implemented later)
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 8),
                Text('Đang in hóa đơn với máy in: ${defaultPrinter.name}'),
              ],
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Text('Lỗi in hóa đơn: $e'),
              ],
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isPrinting = false;
        });
      }
    }
  }

  String _formatCurrency(double amount) {
    return '${amount.toStringAsFixed(0).replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')}₫';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Hóa đơn bán hàng'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go('/pos'),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: _isGeneratingPdf ? null : _shareInvoice,
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              const Center(
                child: Column(
                  children: [
                    Text(
                      'CITY POS',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text('Hóa đơn bán hàng'),
                  ],
                ),
              ),
              const SizedBox(height: 20),

              // Order Info
              Text('Số hóa đơn: ${widget.order.orderNumber}'),
              Text(
                'Ngày: ${widget.order.createdAt?.toString().split(' ')[0] ?? 'N/A'}',
              ),
              Text('Trạng thái: ${widget.order.status}'),
              Text(
                'Phương thức thanh toán: ${widget.order.paymentMethod ?? 'Tiền mặt'}',
              ),

              const SizedBox(height: 20),

              // Customer Info
              if (widget.order.notes != null &&
                  widget.order.notes!.isNotEmpty) ...[
                const Text(
                  'Thông tin khách hàng:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(widget.order.notes!),
                const SizedBox(height: 20),
              ],

              // Items
              const Text(
                'Chi tiết đơn hàng:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 10),

              // Items List
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: widget.order.items.length,
                itemBuilder: (context, index) {
                  final item = widget.order.items[index];
                  return Card(
                    child: ListTile(
                      title: Text(item.productName ?? 'N/A'),
                      subtitle: Text('Số lượng: ${item.quantity}'),
                      trailing: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(_formatCurrency(item.unitPrice)),
                          Text(
                            _formatCurrency(item.totalAmount),
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),

              // Total
              const Divider(),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text('Tạm tính:'),
                  Text(_formatCurrency(widget.order.subtotal)),
                ],
              ),
              if (widget.order.discountAmount > 0)
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text('Giảm giá:'),
                    Text('-${_formatCurrency(widget.order.discountAmount)}'),
                  ],
                ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text('Thu khác:'),
                  Text(_formatCurrency(widget.order.taxAmount)),
                ],
              ),
              const Divider(),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Tổng cộng:',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  Text(
                    _formatCurrency(widget.order.totalAmount),
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
      bottomNavigationBar: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.3),
              blurRadius: 4,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: Row(
          children: [
            // Share button
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _isGeneratingPdf ? null : _shareInvoice,
                icon: _isGeneratingPdf
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.share),
                label: Text(
                  _isGeneratingPdf ? 'Đang chia sẻ...' : 'Chia sẻ PDF',
                ),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
            const SizedBox(width: 12),
            // Print button
            Expanded(
              flex: 2,
              child: ElevatedButton.icon(
                onPressed: _isPrinting ? null : _printInvoice,
                icon: _isPrinting
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      )
                    : const Icon(Icons.print),
                label: Text(_isPrinting ? 'Đang in...' : 'In hóa đơn'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
