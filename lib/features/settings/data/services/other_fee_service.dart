import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../core/config/supabase_config.dart';
import '../../../../data/models/other_fee.dart';

class OtherFeeService {
  static final SupabaseClient _supabase = SupabaseConfig.client;

  // Get all other fees
  static Future<List<OtherFee>> getAllOtherFees() async {
    if (SupabaseConfig.isDemoMode) {
      return _getDemoOtherFees();
    }

    try {
      final response = await _supabase!
          .from('other_fees')
          .select('*')
          .eq('is_active', true)
          .order('name');

      return (response as List)
          .map((json) => OtherFee.fromJson(json))
          .toList();
    } catch (e) {
      print('Error getting other fees: $e');
      return _getDemoOtherFees();
    }
  }

  // Get other fee by ID
  static Future<OtherFee?> getOtherFeeById(String id) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      final demoFees = _getDemoOtherFees();
      try {
        return demoFees.firstWhere((fee) => fee.id == id);
      } catch (e) {
        return null;
      }
    }

    try {
      final response = await _supabase!
          .from('other_fees')
          .select('*')
          .eq('id', id)
          .single();

      return OtherFee.fromJson(response);
    } catch (e) {
      print('Error getting other fee by ID: $e');
      return null;
    }
  }

  // Create new other fee
  static Future<OtherFee> createOtherFee(OtherFee otherFee) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      // In demo mode, just return the fee with a generated ID
      return otherFee.copyWith(
        id: 'demo_${DateTime.now().millisecondsSinceEpoch}',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    }

    try {
      final response = await _supabase!
          .from('other_fees')
          .insert(otherFee.toSupabase())
          .select()
          .single();

      return OtherFee.fromJson(response);
    } catch (e) {
      print('Error creating other fee: $e');
      throw Exception('Lỗi tạo thu khác: $e');
    }
  }

  // Update other fee
  static Future<OtherFee> updateOtherFee(OtherFee otherFee) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      // In demo mode, just return the updated fee
      return otherFee.copyWith(updatedAt: DateTime.now());
    }

    try {
      final updateData = otherFee.toSupabase();
      updateData.remove('created_at'); // Don't update created_at

      final response = await _supabase!
          .from('other_fees')
          .update(updateData)
          .eq('id', otherFee.id)
          .select()
          .single();

      return OtherFee.fromJson(response);
    } catch (e) {
      print('Error updating other fee: $e');
      throw Exception('Lỗi cập nhật thu khác: $e');
    }
  }

  // Delete other fee (soft delete)
  static Future<void> deleteOtherFee(String id) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      // In demo mode, just return success
      return;
    }

    try {
      await _supabase!
          .from('other_fees')
          .update({
            'is_active': false,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', id);
    } catch (e) {
      print('Error deleting other fee: $e');
      throw Exception('Lỗi xóa thu khác: $e');
    }
  }

  // Get active other fees for dropdown
  static Future<List<OtherFee>> getActiveOtherFees() async {
    final allFees = await getAllOtherFees();
    return allFees.where((fee) => fee.isActive).toList();
  }

  // Demo data for testing
  static List<OtherFee> _getDemoOtherFees() {
    return [
      OtherFee(
        id: 'demo_1',
        name: 'Phí giao hàng',
        description: 'Phí giao hàng tận nơi',
        amount: 20000,
        type: 'fixed',
        isActive: true,
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now().subtract(const Duration(days: 30)),
      ),
      OtherFee(
        id: 'demo_2',
        name: 'Phí dịch vụ',
        description: 'Phí dịch vụ 5%',
        amount: 5,
        type: 'percentage',
        isActive: true,
        createdAt: DateTime.now().subtract(const Duration(days: 25)),
        updatedAt: DateTime.now().subtract(const Duration(days: 25)),
      ),
      OtherFee(
        id: 'demo_3',
        name: 'Phí đóng gói',
        description: 'Phí đóng gói sản phẩm',
        amount: 5000,
        type: 'fixed',
        isActive: true,
        createdAt: DateTime.now().subtract(const Duration(days: 20)),
        updatedAt: DateTime.now().subtract(const Duration(days: 20)),
      ),
      OtherFee(
        id: 'demo_4',
        name: 'Thuế VAT',
        description: 'Thuế giá trị gia tăng 10%',
        amount: 10,
        type: 'percentage',
        isActive: true,
        createdAt: DateTime.now().subtract(const Duration(days: 15)),
        updatedAt: DateTime.now().subtract(const Duration(days: 15)),
      ),
    ];
  }

  // Calculate total other fees for an order
  static double calculateTotalOtherFees(
    List<OtherFee> selectedFees,
    double subtotal,
  ) {
    return selectedFees.fold(0.0, (total, fee) {
      return total + fee.calculateFee(subtotal);
    });
  }

  // Convert selected fees to OrderOtherFee list
  static List<OrderOtherFee> convertToOrderOtherFees({
    required String orderId,
    required List<OtherFee> selectedFees,
    required double subtotal,
  }) {
    return selectedFees.map((fee) {
      return OrderOtherFee.fromOtherFee(
        orderId: orderId,
        otherFee: fee,
        subtotal: subtotal,
      );
    }).toList();
  }
}
