import 'package:flutter/material.dart';

import '../../../data/models/other_fee.dart';
import '../data/services/other_fee_service.dart';

class AddEditOtherFeeScreen extends StatefulWidget {
  final OtherFee? fee;

  const AddEditOtherFeeScreen({
    super.key,
    this.fee,
  });

  @override
  State<AddEditOtherFeeScreen> createState() => _AddEditOtherFeeScreenState();
}

class _AddEditOtherFeeScreenState extends State<AddEditOtherFeeScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _amountController = TextEditingController();
  String _selectedType = 'fixed';
  bool _isActive = true;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.fee != null) {
      _nameController.text = widget.fee!.name;
      _descriptionController.text = widget.fee!.description;
      _amountController.text = widget.fee!.amount.toString();
      _selectedType = widget.fee!.type;
      _isActive = widget.fee!.isActive;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _amountController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.fee == null ? 'Thêm thu khác' : 'Sửa thu khác'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            )
          else
            TextButton(
              onPressed: _saveFee,
              child: const Text(
                'Lưu',
                style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
              ),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            // Name field
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Tên thu khác *',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.receipt_long),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Vui lòng nhập tên thu khác';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Description field
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Mô tả',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.description),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),

            // Type dropdown
            DropdownButtonFormField<String>(
              value: _selectedType,
              decoration: const InputDecoration(
                labelText: 'Loại thu khác *',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.category),
              ),
              items: const [
                DropdownMenuItem(
                  value: 'fixed',
                  child: Row(
                    children: [
                      Icon(Icons.attach_money, color: Colors.green),
                      SizedBox(width: 8),
                      Text('Cố định (VNĐ)'),
                    ],
                  ),
                ),
                DropdownMenuItem(
                  value: 'percentage',
                  child: Row(
                    children: [
                      Icon(Icons.percent, color: Colors.orange),
                      SizedBox(width: 8),
                      Text('Phần trăm (%)'),
                    ],
                  ),
                ),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedType = value!;
                });
              },
            ),
            const SizedBox(height: 16),

            // Amount field
            TextFormField(
              controller: _amountController,
              decoration: InputDecoration(
                labelText: _selectedType == 'fixed' 
                    ? 'Số tiền (VNĐ) *' 
                    : 'Phần trăm (%) *',
                border: const OutlineInputBorder(),
                prefixIcon: Icon(_selectedType == 'fixed' ? Icons.money : Icons.percent),
                suffixText: _selectedType == 'fixed' ? '₫' : '%',
              ),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Vui lòng nhập số tiền';
                }
                final amount = double.tryParse(value);
                if (amount == null || amount <= 0) {
                  return 'Số tiền phải lớn hơn 0';
                }
                if (_selectedType == 'percentage' && amount > 100) {
                  return 'Phần trăm không được vượt quá 100%';
                }
                return null;
              },
            ),
            const SizedBox(height: 24),

            // Active switch
            Card(
              child: SwitchListTile(
                title: const Text('Trạng thái'),
                subtitle: Text(_isActive ? 'Đang hoạt động' : 'Tạm dừng'),
                value: _isActive,
                onChanged: (value) {
                  setState(() {
                    _isActive = value;
                  });
                },
                secondary: Icon(
                  _isActive ? Icons.toggle_on : Icons.toggle_off,
                  color: _isActive ? Colors.green : Colors.grey,
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Preview card
            if (_nameController.text.isNotEmpty && _amountController.text.isNotEmpty) ...[
              Card(
                color: Colors.blue.withValues(alpha: 0.1),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Icon(Icons.preview, color: Colors.blue),
                          const SizedBox(width: 8),
                          const Text(
                            'Xem trước',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.blue,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Text(
                        _nameController.text,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      if (_descriptionController.text.isNotEmpty) ...[
                        const SizedBox(height: 4),
                        Text(_descriptionController.text),
                      ],
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: _selectedType == 'fixed' ? Colors.green : Colors.orange,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              _selectedType == 'fixed' ? 'Cố định' : 'Phần trăm',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            _selectedType == 'fixed'
                                ? '${_amountController.text}₫'
                                : '${_amountController.text}%',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.green,
                            ),
                          ),
                          const Spacer(),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: _isActive ? Colors.green : Colors.red,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              _isActive ? 'Hoạt động' : 'Tạm dừng',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),
            ],

            // Save button
            ElevatedButton(
              onPressed: _isLoading ? null : _saveFee,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                minimumSize: const Size(double.infinity, 50),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: _isLoading
                  ? const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        ),
                        SizedBox(width: 12),
                        Text('Đang lưu...'),
                      ],
                    )
                  : Text(
                      widget.fee == null ? 'Thêm thu khác' : 'Cập nhật thu khác',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _saveFee() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final amount = double.parse(_amountController.text);
      
      if (widget.fee == null) {
        // Create new fee
        final newFee = OtherFee(
          id: '', // Will be generated by service
          name: _nameController.text.trim(),
          description: _descriptionController.text.trim(),
          amount: amount,
          type: _selectedType,
          isActive: _isActive,
          createdAt: DateTime.now(),
        );
        
        await OtherFeeService.createOtherFee(newFee);
      } else {
        // Update existing fee
        final updatedFee = widget.fee!.copyWith(
          name: _nameController.text.trim(),
          description: _descriptionController.text.trim(),
          amount: amount,
          type: _selectedType,
          isActive: _isActive,
          updatedAt: DateTime.now(),
        );
        
        await OtherFeeService.updateOtherFee(updatedFee);
      }

      if (mounted) {
        Navigator.of(context).pop(true); // Return true to indicate success
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.fee == null 
                  ? 'Đã thêm thu khác thành công' 
                  : 'Đã cập nhật thu khác thành công',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi lưu thu khác: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
