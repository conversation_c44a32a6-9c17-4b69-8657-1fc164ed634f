import 'package:city_pos/data/models/other_fee.dart';
import 'package:city_pos/features/settings/data/services/other_fee_service.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('OtherFee Model Tests', () {
    test('should create OtherFee with correct properties', () {
      final fee = OtherFee(
        id: 'test_id',
        name: 'Test Fee',
        description: 'Test Description',
        amount: 100.0,
        type: 'fixed',
        isActive: true,
      );

      expect(fee.id, 'test_id');
      expect(fee.name, 'Test Fee');
      expect(fee.description, 'Test Description');
      expect(fee.amount, 100.0);
      expect(fee.type, 'fixed');
      expect(fee.isActive, true);
    });

    test('should calculate fixed fee correctly', () {
      final fee = OtherFee(
        id: 'test_id',
        name: 'Fixed Fee',
        description: 'Fixed amount fee',
        amount: 50000.0,
        type: 'fixed',
      );

      final calculatedFee = fee.calculateFee(100000.0);
      expect(calculatedFee, 50000.0);
    });

    test('should calculate percentage fee correctly', () {
      final fee = OtherFee(
        id: 'test_id',
        name: 'Percentage Fee',
        description: 'Percentage based fee',
        amount: 10.0, // 10%
        type: 'percentage',
      );

      final calculatedFee = fee.calculateFee(100000.0);
      expect(calculatedFee, 10000.0); // 10% of 100,000
    });

    test('should convert to JSON correctly', () {
      final fee = OtherFee(
        id: 'test_id',
        name: 'Test Fee',
        description: 'Test Description',
        amount: 100.0,
        type: 'fixed',
        isActive: true,
      );

      final json = fee.toJson();
      expect(json['id'], 'test_id');
      expect(json['name'], 'Test Fee');
      expect(json['description'], 'Test Description');
      expect(json['amount'], 100.0);
      expect(json['type'], 'fixed');
      expect(json['is_active'], true);
    });

    test('should create from JSON correctly', () {
      final json = {
        'id': 'test_id',
        'name': 'Test Fee',
        'description': 'Test Description',
        'amount': 100.0,
        'type': 'fixed',
        'is_active': true,
      };

      final fee = OtherFee.fromJson(json);
      expect(fee.id, 'test_id');
      expect(fee.name, 'Test Fee');
      expect(fee.description, 'Test Description');
      expect(fee.amount, 100.0);
      expect(fee.type, 'fixed');
      expect(fee.isActive, true);
    });

    test('should copy with new values correctly', () {
      final originalFee = OtherFee(
        id: 'test_id',
        name: 'Original Fee',
        description: 'Original Description',
        amount: 100.0,
        type: 'fixed',
        isActive: true,
      );

      final copiedFee = originalFee.copyWith(
        name: 'Updated Fee',
        amount: 200.0,
      );

      expect(copiedFee.id, 'test_id');
      expect(copiedFee.name, 'Updated Fee');
      expect(copiedFee.description, 'Original Description');
      expect(copiedFee.amount, 200.0);
      expect(copiedFee.type, 'fixed');
      expect(copiedFee.isActive, true);
    });
  });

  group('OrderOtherFee Model Tests', () {
    test('should create OrderOtherFee from OtherFee correctly', () {
      final otherFee = OtherFee(
        id: 'fee_id',
        name: 'Test Fee',
        description: 'Test Description',
        amount: 10.0, // 10%
        type: 'percentage',
        isActive: true,
      );

      final orderOtherFee = OrderOtherFee.fromOtherFee(
        orderId: 'order_id',
        otherFee: otherFee,
        subtotal: 100000.0,
      );

      expect(orderOtherFee.orderId, 'order_id');
      expect(orderOtherFee.otherFeeId, 'fee_id');
      expect(orderOtherFee.name, 'Test Fee');
      expect(orderOtherFee.amount, 10.0);
      expect(orderOtherFee.type, 'percentage');
      expect(orderOtherFee.calculatedAmount, 10000.0); // 10% of 100,000
    });

    test('should convert to JSON correctly', () {
      final orderOtherFee = OrderOtherFee(
        id: 'order_fee_id',
        orderId: 'order_id',
        otherFeeId: 'fee_id',
        name: 'Test Fee',
        amount: 10.0,
        type: 'percentage',
        calculatedAmount: 10000.0,
      );

      final json = orderOtherFee.toJson();
      expect(json['id'], 'order_fee_id');
      expect(json['order_id'], 'order_id');
      expect(json['other_fee_id'], 'fee_id');
      expect(json['name'], 'Test Fee');
      expect(json['amount'], 10.0);
      expect(json['type'], 'percentage');
      expect(json['calculated_amount'], 10000.0);
    });
  });

  group('OtherFeeService Tests', () {
    test('should calculate total other fees correctly', () {
      final fees = [
        OtherFee(
          id: 'fee1',
          name: 'Fixed Fee',
          description: 'Fixed amount fee',
          amount: 20000.0,
          type: 'fixed',
        ),
        OtherFee(
          id: 'fee2',
          name: 'Percentage Fee',
          description: 'Percentage based fee',
          amount: 5.0, // 5%
          type: 'percentage',
        ),
      ];

      final total = OtherFeeService.calculateTotalOtherFees(fees, 100000.0);
      expect(total, 25000.0); // 20,000 + 5,000 (5% of 100,000)
    });

    test('should convert to OrderOtherFee list correctly', () {
      final fees = [
        OtherFee(
          id: 'fee1',
          name: 'Fixed Fee',
          description: 'Fixed amount fee',
          amount: 20000.0,
          type: 'fixed',
        ),
        OtherFee(
          id: 'fee2',
          name: 'Percentage Fee',
          description: 'Percentage based fee',
          amount: 5.0, // 5%
          type: 'percentage',
        ),
      ];

      final orderOtherFees = OtherFeeService.convertToOrderOtherFees(
        orderId: 'test_order',
        selectedFees: fees,
        subtotal: 100000.0,
      );

      expect(orderOtherFees.length, 2);
      expect(orderOtherFees[0].orderId, 'test_order');
      expect(orderOtherFees[0].otherFeeId, 'fee1');
      expect(orderOtherFees[0].calculatedAmount, 20000.0);
      expect(orderOtherFees[1].otherFeeId, 'fee2');
      expect(orderOtherFees[1].calculatedAmount, 5000.0);
    });

    test('should get demo other fees', () {
      // Test demo data directly without calling service
      final demoFees = [
        OtherFee(
          id: 'demo_1',
          name: 'Phí giao hàng',
          description: 'Phí giao hàng tận nơi',
          amount: 20000,
          type: 'fixed',
          isActive: true,
        ),
        OtherFee(
          id: 'demo_2',
          name: 'Phí dịch vụ',
          description: 'Phí dịch vụ 5%',
          amount: 5,
          type: 'percentage',
          isActive: true,
        ),
      ];

      expect(demoFees.isNotEmpty, true);
      expect(demoFees.length, 2);

      // Check if demo fees have expected properties
      final deliveryFee = demoFees.firstWhere(
        (fee) => fee.name == 'Phí giao hàng',
      );
      expect(deliveryFee.amount, 20000);
      expect(deliveryFee.type, 'fixed');

      final serviceFee = demoFees.firstWhere(
        (fee) => fee.name == 'Phí dịch vụ',
      );
      expect(serviceFee.amount, 5);
      expect(serviceFee.type, 'percentage');
    });
  });
}
