import 'package:flutter_test/flutter_test.dart';
import 'package:city_pos/core/services/invoice_service.dart';
import 'package:city_pos/data/models/order.dart';
import 'package:city_pos/data/models/order_item.dart';
import 'package:city_pos/data/models/other_fee.dart';

void main() {
  group('Invoice Other Fees Tests', () {
    test('should calculate other fees correctly in invoice data', () async {
      // Create a test order with other fees
      final order = Order(
        id: 'test-order-id',
        orderNumber: 'HD123456789',
        type: 'sale',
        status: 'completed',
        subtotal: 100000,
        taxAmount: 0, // This will be replaced by other fees
        discountAmount: 5000,
        totalAmount: 105000, // 100000 + 10000 (other fees) - 5000 (discount)
        paymentStatus: 'paid',
        paymentMethod: 'cash',
        items: [
          OrderItem(
            id: 'item-1',
            orderId: 'test-order-id',
            productId: 'product-1',
            productName: 'Test Product',
            quantity: 2,
            unitPrice: 50000,
            totalAmount: 100000,
            totalPrice: 100000,
          ),
        ],
        otherFees: [
          OrderOtherFee(
            id: 'fee-1',
            orderId: 'test-order-id',
            otherFeeId: 'other-fee-1',
            name: '<PERSON><PERSON> vận chuyển',
            amount: 10000,
            type: 'fixed',
            calculatedAmount: 10000,
          ),
        ],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Test that other fees are calculated correctly
      final otherFeesTotal = order.otherFees.fold(
        0.0,
        (sum, fee) => sum + fee.calculatedAmount,
      );

      expect(otherFeesTotal, equals(10000.0));
      
      // Test that total calculation is correct
      final expectedTotal = order.subtotal + otherFeesTotal - order.discountAmount;
      expect(expectedTotal, equals(105000.0));
      expect(order.totalAmount, equals(expectedTotal));

      print('✅ Other fees calculation test passed');
      print('   - Subtotal: ${order.subtotal}₫');
      print('   - Other fees: ${otherFeesTotal}₫');
      print('   - Discount: ${order.discountAmount}₫');
      print('   - Total: ${order.totalAmount}₫');
    });

    test('should handle invoice creation with multiple other fees', () async {
      // Create a test order with multiple other fees
      final order = Order(
        id: 'test-order-id-2',
        orderNumber: 'HD123456790',
        type: 'sale',
        status: 'completed',
        subtotal: 200000,
        taxAmount: 0,
        discountAmount: 10000,
        totalAmount: 215000, // 200000 + 25000 (other fees) - 10000 (discount)
        paymentStatus: 'paid',
        paymentMethod: 'cash',
        items: [
          OrderItem(
            id: 'item-2',
            orderId: 'test-order-id-2',
            productId: 'product-2',
            productName: 'Test Product 2',
            quantity: 4,
            unitPrice: 50000,
            totalAmount: 200000,
            totalPrice: 200000,
          ),
        ],
        otherFees: [
          OrderOtherFee(
            id: 'fee-2',
            orderId: 'test-order-id-2',
            otherFeeId: 'other-fee-2',
            name: 'Phí vận chuyển',
            amount: 15000,
            type: 'fixed',
            calculatedAmount: 15000,
          ),
          OrderOtherFee(
            id: 'fee-3',
            orderId: 'test-order-id-2',
            otherFeeId: 'other-fee-3',
            name: 'Phí dịch vụ',
            amount: 5, // 5% of subtotal
            type: 'percentage',
            calculatedAmount: 10000, // 5% of 200000
          ),
        ],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Test multiple other fees calculation
      final otherFeesTotal = order.otherFees.fold(
        0.0,
        (sum, fee) => sum + fee.calculatedAmount,
      );

      expect(otherFeesTotal, equals(25000.0)); // 15000 + 10000
      
      // Test that total calculation is correct
      final expectedTotal = order.subtotal + otherFeesTotal - order.discountAmount;
      expect(expectedTotal, equals(215000.0));
      expect(order.totalAmount, equals(expectedTotal));

      print('✅ Multiple other fees calculation test passed');
      print('   - Subtotal: ${order.subtotal}₫');
      print('   - Other fees total: ${otherFeesTotal}₫');
      print('     * Phí vận chuyển: 15,000₫');
      print('     * Phí dịch vụ (5%): 10,000₫');
      print('   - Discount: ${order.discountAmount}₫');
      print('   - Total: ${order.totalAmount}₫');
    });
  });
}
