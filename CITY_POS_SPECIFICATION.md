# ĐẶC TẢ TÍNH NĂNG HỆ THỐNG CITY POS

## 📋 **TỔNG QUAN HỆ THỐNG**

**City POS** là hệ thống quản lý bán hàng tích hợp đầy đủ, bao gồm: <PERSON><PERSON> b<PERSON>, quản lý tồn kho, xu<PERSON><PERSON> nh<PERSON><PERSON> kho, quản lý đối tác, báo cáo và in hóa đơn.

**Công nghệ**: Flutter + Supabase + Riverpod + Hive
**Nền tảng**: Mobile, Tablet, Desktop, Web
**Ngôn ngữ**: Ti<PERSON><PERSON> Vi<PERSON>, Ti<PERSON><PERSON> An<PERSON>, Tiếng Nhật

---

## 🔗 **QUAN HỆ GIỮA CÁC MODULE**

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   AUTHENTICATION│    │    DASHBOARD    │    │   SETTINGS      │
│                 │    │                 │    │                 │
│ • Đăng nhập     │────│ • Thống kê      │────│ • Cài đặt app   │
│ • Phân quyền    │    │ • Quick actions │    │ • Cài đặt máy in│
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         │              │   NOTIFICATIONS │              │
         │              │                 │              │
         └──────────────│ • <PERSON>hông báo     │──────────────┘
                        │ • Push alerts   │
                        └─────────────────┘
                                 │
    ┌─────────────────┬──────────┼──────────┬─────────────────┐
    │                 │          │          │                 │
┌───▼───┐    ┌────────▼──┐   ┌───▼────┐   ┌▼─────────┐   ┌───▼────┐
│  POS  │    │INVENTORY  │   │FINANCE │   │PARTNERS  │   │REPORTS │
│       │    │           │   │        │   │          │   │        │
│ Bán   │◄──►│ Sản phẩm  │   │ Thu/Chi│   │ KH & NCC │   │ Báo cáo│
│ hàng  │    │ Tồn kho   │   │ Sổ quỹ │   │ Công nợ  │   │ Thống kê│
└───┬───┘    │ Xuất/Nhập │   └────────┘   └──────────┘   └────────┘
    │        └───────────┘
    │
┌───▼────┐
│INVOICES│
│        │
│ Hóa đơn│
│ In ấn  │
└────────┘
```

---

## 📱 **CÁC TÍNH NĂNG CHI TIẾT**

## 1. **🔐 AUTHENTICATION - HỆ THỐNG XÁC THỰC**

### **Mục đích**: Quản lý truy cập và bảo mật hệ thống

### **Tính năng chính**:
- **Đăng nhập**: Email + Password qua Supabase Auth
- **Đăng ký**: Tạo tài khoản mới với thông tin cơ bản
- **Remember Login**: Lưu thông tin đăng nhập local (Hive)
- **Auto User Creation**: Tự động tạo user nếu chưa tồn tại
- **Demo Mode**: Chế độ offline không cần backend

### **Flow đăng nhập**:
```
1. User nhập email/password
2. Kiểm tra "Remember Login" checkbox
3. Gọi AuthService.signIn()
4. Nếu user không tồn tại → Tự động tạo user mới
5. Lưu credentials nếu "Remember Login" = true
6. Chuyển đến Dashboard
```

### **Ràng buộc**:
- **Email**: Phải đúng định dạng email
- **Password**: Tối thiểu 6 ký tự
- **Session**: Tự động expire theo Supabase config
- **Multi-tenant**: Mỗi user chỉ thấy dữ liệu của mình (RLS)

### **Quan hệ với module khác**:
- **→ Dashboard**: Sau khi đăng nhập thành công
- **→ All Modules**: Cung cấp user context cho tất cả tính năng
- **→ Notifications**: User ID để gửi thông báo cá nhân

---

## 2. **📊 DASHBOARD - TRANG CHÍNH**

### **Mục đích**: Tổng quan tình hình kinh doanh và điều hướng nhanh

### **Thống kê hiển thị**:
- **Doanh thu hôm nay**: Tổng tiền từ orders có status = 'completed'
- **Số đơn hàng hôm nay**: Đếm orders trong ngày hiện tại
- **Sản phẩm sắp hết**: Products có stock_quantity < min_stock_level
- **Tổng khách hàng**: Partners có type = 'customer' và is_active = true

### **Quick Actions**:
- **Bán hàng**: → POS Screen
- **Thêm sản phẩm**: → Add Product Screen
- **Xuất nhập kho**: → Stock Transactions Screen
- **Xem báo cáo**: → Reports Screen

### **Flow hoạt động**:
```
1. Load dashboard data từ DashboardService
2. Hiển thị 4 thống kê chính (responsive grid)
3. Auto-refresh mỗi khi navigate back từ screen khác
4. Pull-to-refresh để cập nhật thủ công
5. Click Quick Action → Navigate đến screen tương ứng
```

### **Ràng buộc**:
- **Real-time**: Dữ liệu phải cập nhật khi có thay đổi
- **Performance**: Load nhanh với parallel queries
- **Responsive**: Hiển thị khác nhau trên mobile/desktop

### **Quan hệ với module khác**:
- **← Orders**: Lấy dữ liệu doanh thu và số đơn hàng
- **← Products**: Lấy thông tin sản phẩm sắp hết hàng
- **← Partners**: Đếm số lượng khách hàng
- **→ All Modules**: Điều hướng đến các tính năng khác

---

## 3. **🛒 POS - HỆ THỐNG BÁN HÀNG**

### **Mục đích**: Xử lý giao dịch bán hàng từ A-Z

### **Tính năng chính**:
- **Multi-tab POS**: Nhiều đơn hàng cùng lúc (POSNotifier với tabs)
- **Product selection**: Grid sản phẩm với category filter và search
- **Barcode scanner**: Quét mã vạch thêm sản phẩm
- **Cart management**: Thêm/xóa/điều chỉnh số lượng
- **Payment processing**: Thanh toán với nhiều phương thức
- **Invoice generation**: Tạo và hiển thị hóa đơn

### **Flow bán hàng**:
```
1. Chọn sản phẩm từ grid hoặc scan barcode
2. Sản phẩm được thêm vào cart với quantity = 1
3. Điều chỉnh quantity hoặc xóa sản phẩm nếu cần
4. Nhập thông tin khách hàng (optional)
5. Chọn payment method và nhập số tiền
6. Validate: Kiểm tra stock availability
7. Tạo Order → Trừ inventory → Tạo notification
8. Hiển thị invoice → Option in hóa đơn
9. Clear cart và tạo tab mới
```

### **Ràng buộc nghiệp vụ**:
- **Stock validation**: Phải đủ hàng mới được bán
- **Payment validation**: Số tiền trả >= tổng tiền
- **Auto inventory update**: Tự động trừ tồn kho khi hoàn thành
- **Order numbering**: Tự động tạo mã đơn duy nhất
- **Cart persistence**: Chỉ lưu đơn chưa thanh toán

### **Quan hệ với module khác**:
- **← Products**: Lấy danh sách sản phẩm để bán
- **← Categories**: Filter sản phẩm theo danh mục
- **→ Orders**: Tạo đơn hàng mới
- **→ Inventory**: Trừ tồn kho tự động
- **→ Invoices**: Tạo hóa đơn
- **→ Notifications**: Thông báo đơn hàng mới
- **→ Finance**: Ghi nhận doanh thu (gián tiếp)

---

## 4. **📦 INVENTORY - QUẢN LÝ TỒN KHO**

### **Mục đích**: Quản lý sản phẩm, danh mục và xuất nhập kho

### **4.1 Quản lý sản phẩm**
- **Product CRUD**: Thêm/sửa/xóa/xem sản phẩm
- **Product info**: Tên, mô tả, SKU, barcode, giá bán, giá vốn, đơn vị
- **Stock management**: Tồn kho hiện tại, tối thiểu, tối đa
- **Image upload**: Upload hình ảnh qua Supabase Storage
- **Category assignment**: Gán sản phẩm vào danh mục

### **4.2 Quản lý danh mục**
- **Category CRUD**: Thêm/sửa/xóa danh mục
- **Visual identity**: Màu sắc và icon cho danh mục
- **Product filtering**: Lọc sản phẩm theo danh mục

### **4.3 Xuất nhập kho (Stock Transactions)**
- **Transaction types**: Import (nhập), Export (xuất)
- **Multi-product transactions**: Nhiều sản phẩm trong một phiếu
- **Cost tracking**: Giá vốn cho từng item
- **Transaction history**: Lịch sử với filter và search
- **Statistics**: Thống kê tổng quan xuất nhập

### **Flow xuất nhập kho**:
```
1. Vào Stock Transactions Screen
2. Click "Thêm giao dịch" → Add Stock Transaction Screen
3. Chọn loại: Import hoặc Export
4. Thêm sản phẩm: Chọn product + quantity + unit_price
5. Nhập ghi chú (optional)
6. Save → Tạo StockTransaction + StockTransactionItems
7. Cập nhật stock_quantity của products
8. Gửi notification về thay đổi tồn kho
```

### **Ràng buộc**:
- **Stock validation**: Export không được vượt quá tồn kho hiện tại
- **Price validation**: Unit price phải > 0
- **Quantity validation**: Quantity phải > 0
- **Auto stock update**: Tự động cập nhật product.stock_quantity

### **Quan hệ với module khác**:
- **← Categories**: Phân loại sản phẩm
- **→ POS**: Cung cấp sản phẩm để bán
- **→ Dashboard**: Cảnh báo low stock
- **→ Reports**: Dữ liệu cho báo cáo tồn kho
- **← Orders**: Tự động trừ tồn kho khi bán

---

## 5. **💰 FINANCE - QUẢN LÝ TÀI CHÍNH**

### **Mục đích**: Theo dõi dòng tiền thu/chi và tình hình tài chính

### **Tính năng chính**:
- **Finance transactions**: Quản lý giao dịch thu/chi
- **Cash flow tracking**: Theo dõi dòng tiền
- **Balance calculation**: Tính toán số dư
- **Financial reports**: Báo cáo tài chính cơ bản

### **Flow quản lý tài chính**:
```
1. Vào Finance Screen → Hiển thị danh sách giao dịch
2. Add Transaction → Chọn loại (Thu/Chi)
3. Nhập: Số tiền, Danh mục, Ghi chú, Ngày
4. Save → Tạo finance transaction
5. Auto update balance và cash flow
6. Hiển thị trong reports
```

### **Ràng buộc**:
- **Amount validation**: Số tiền phải > 0
- **Category required**: Phải chọn danh mục giao dịch
- **Date validation**: Ngày không được trong tương lai
- **Balance tracking**: Tự động tính số dư

### **Quan hệ với module khác**:
- **← POS**: Tự động ghi nhận doanh thu từ bán hàng
- **→ Reports**: Cung cấp dữ liệu cho báo cáo tài chính
- **→ Dashboard**: Hiển thị doanh thu hôm nay

---

## 6. **🤝 PARTNERS - QUẢN LÝ ĐỐI TÁC**

### **Mục đích**: Quản lý thông tin khách hàng và nhà cung cấp

### **Tính năng chính**:
- **Partner CRUD**: Thêm/sửa/xóa/xem đối tác
- **Partner types**: Customer, Supplier, Both
- **Contact management**: Thông tin liên lạc đầy đủ
- **Credit management**: Hạn mức tín dụng và công nợ
- **Search & filter**: Tìm kiếm theo tên, loại, trạng thái

### **Thông tin đối tác**:
- **Basic info**: Tên, email, phone, địa chỉ
- **Business info**: Mã số thuế, loại đối tác
- **Financial info**: Hạn mức tín dụng, số dư hiện tại
- **Status**: Trạng thái hoạt động

### **Flow quản lý đối tác**:
```
1. Vào Partners Screen → Danh sách đối tác
2. Add Partner → Nhập thông tin cơ bản
3. Chọn type: Customer/Supplier/Both
4. Set credit limit (nếu cần)
5. Save → Tạo partner record
6. Có thể edit/deactivate sau này
```

### **Ràng buộc**:
- **Name required**: Tên đối tác bắt buộc
- **Email format**: Email phải đúng định dạng
- **Credit limit**: Phải >= 0
- **Type validation**: Phải chọn một trong 3 loại

### **Quan hệ với module khác**:
- **→ POS**: Chọn khách hàng khi bán hàng
- **→ Orders**: Liên kết đơn hàng với khách hàng
- **→ Dashboard**: Đếm tổng số khách hàng
- **→ Reports**: Báo cáo theo đối tác

---

## 7. **📋 INVOICES - QUẢN LÝ HÓA ĐƠN**

### **Mục đích**: Hiển thị và quản lý hóa đơn bán hàng

### **Tính năng chính**:
- **Invoice display**: Hiển thị hóa đơn sau thanh toán POS
- **Invoice management**: Quản lý danh sách hóa đơn
- **PDF generation**: Tạo file PDF hóa đơn
- **Print support**: In hóa đơn qua máy in mạng
- **Share functionality**: Chia sẻ hóa đơn

### **Flow hóa đơn**:
```
1. POS hoàn thành thanh toán → Tự động tạo Order
2. Nếu setting "Show invoice after payment" = true
   → Navigate to Invoice Display Screen
3. Hiển thị thông tin đầy đủ: Order details, items, customer
4. User có thể: In hóa đơn, Share PDF, Back to POS
5. Invoice được lưu trong Invoices Screen để xem lại
```

### **Thông tin hóa đơn**:
- **Order information**: Mã đơn, ngày, tổng tiền
- **Customer details**: Tên, phone khách hàng (nếu có)
- **Items list**: Danh sách sản phẩm, số lượng, giá
- **Payment info**: Phương thức thanh toán, tiền thừa
- **Company info**: Logo, tên, địa chỉ công ty

### **Ràng buộc**:
- **Auto generation**: Chỉ tạo từ completed orders
- **Print validation**: Kiểm tra máy in trước khi in
- **PDF format**: Định dạng chuẩn cho hóa đơn

### **Quan hệ với module khác**:
- **← POS**: Nhận order data để tạo hóa đơn
- **← Settings**: Cài đặt hiển thị và in hóa đơn
- **← Printing**: Sử dụng printer service để in

---

## 8. **📊 REPORTS - BÁO CÁO**

### **Mục đích**: Cung cấp thông tin phân tích kinh doanh

### **Loại báo cáo**:
- **Sales Report**: Doanh thu theo thời gian với biểu đồ
- **Inventory Report**: Tình hình tồn kho và giá trị
- **Top Products**: Sản phẩm bán chạy nhất
- **Financial Summary**: Tổng quan tài chính cơ bản

### **Tính năng báo cáo**:
- **Date range filter**: Chọn khoảng thời gian báo cáo
- **Chart visualization**: Biểu đồ với fl_chart
- **Data tables**: Bảng dữ liệu chi tiết
- **Responsive design**: Hiển thị tối ưu trên mọi thiết bị

### **Flow báo cáo**:
```
1. Vào Reports Screen → Chọn loại báo cáo
2. Set date range (start date, end date)
3. Load data từ ReportsService
4. Hiển thị chart + data table
5. User có thể thay đổi period (daily/weekly/monthly)
```

### **Nguồn dữ liệu**:
- **Sales data**: Từ completed orders
- **Inventory data**: Từ products và stock transactions
- **Financial data**: Từ finance transactions
- **Demo mode**: Sử dụng generated demo data

### **Quan hệ với module khác**:
- **← Orders**: Dữ liệu doanh thu và bán hàng
- **← Products**: Thông tin sản phẩm và tồn kho
- **← Finance**: Dữ liệu tài chính
- **← Stock Transactions**: Lịch sử xuất nhập kho

---

## 9. **🔔 NOTIFICATIONS - HỆ THỐNG THÔNG BÁO**

### **Mục đích**: Thông báo sự kiện quan trọng cho người dùng

### **Loại thông báo**:
- **Order notifications**: Đơn hàng mới, thanh toán thành công
- **Stock alerts**: Sản phẩm sắp hết hàng
- **System notifications**: Lỗi hệ thống, cập nhật
- **Payment notifications**: Giao dịch tài chính

### **Cơ chế thông báo**:
- **In-app notifications**: Hiển thị trong app với badge
- **Push notifications**: FCM cho mobile devices
- **Auto trigger**: Tự động khi có sự kiện
- **Real-time**: Cập nhật ngay lập tức

### **Flow thông báo**:
```
1. Sự kiện xảy ra (order created, low stock, etc.)
2. NotificationService.createNotification()
3. Lưu vào database với user_id
4. Gửi push notification (nếu enabled)
5. Update notification badge trong app
6. User click → Mark as read
```

### **Cài đặt thông báo**:
- **Notification preferences**: Bật/tắt từng loại
- **Push notifications**: Bật/tắt push notifications
- **Email notifications**: Bật/tắt email notifications

### **Quan hệ với module khác**:
- **← POS**: Thông báo đơn hàng mới
- **← Inventory**: Cảnh báo low stock
- **← Finance**: Thông báo giao dịch tài chính
- **← System**: Thông báo lỗi và cập nhật

---

## 10. **🖨️ PRINTING - HỆ THỐNG IN ẤN**

### **Mục đích**: In hóa đơn qua máy in mạng

### **Tính năng chính**:
- **Printer management**: Quản lý danh sách máy in
- **Network printing**: In qua IP/Port (LAN/WiFi)
- **Invoice printing**: In hóa đơn sau thanh toán
- **PDF generation**: Tạo PDF trước khi in

### **Flow thiết lập máy in**:
```
1. Settings → Thiết lập máy in
2. Add Printer → Nhập IP, Port, Tên máy in
3. Test connection → Kiểm tra kết nối
4. Set as default → Đặt làm máy in mặc định
5. Save printer configuration
```

### **Flow in hóa đơn**:
```
1. POS hoàn thành thanh toán → Invoice Display
2. Click "In hóa đơn" button
3. Kiểm tra default printer có tồn tại
4. Generate PDF từ order data
5. Send to printer qua network
6. Hiển thị kết quả (success/error)
```

### **Cấu hình máy in**:
- **Printer info**: Tên, IP address, Port (default 9100)
- **Connection**: Network printer qua TCP/IP
- **Status**: Active/Inactive, Default printer
- **Validation**: Test connection trước khi lưu

### **Quan hệ với module khác**:
- **← Settings**: Cài đặt và quản lý máy in
- **← Invoices**: Nhận lệnh in hóa đơn
- **← PDF Service**: Tạo PDF để in

---

## 11. **⚙️ CÀI ĐẶT HỆ THỐNG (SETTINGS)**

### **11.1 Cài đặt ứng dụng**
- **Language selection**: Chọn ngôn ngữ (VI/EN/JP)
- **Theme settings**: Cài đặt giao diện
- **Currency settings**: Cài đặt tiền tệ
- **Date/time format**: Định dạng ngày/giờ
- **Number format**: Định dạng số

### **11.2 Cài đặt hóa đơn**
- **Invoice templates**: Mẫu hóa đơn
- **Company information**: Thông tin công ty
- **Logo upload**: Upload logo công ty
- **Invoice numbering**: Đánh số hóa đơn
- **Auto-show invoice**: Tự động hiển thị hóa đơn sau thanh toán

### **11.3 Cài đặt máy in**
- **Printer setup**: Thiết lập máy in
- **Print preferences**: Tùy chọn in
- **Paper size**: Kích thước giấy
- **Print quality**: Chất lượng in
- **Auto-print**: Tự động in

### **11.4 Cài đặt bảo mật**
- **Password change**: Đổi mật khẩu
- **Session timeout**: Thời gian hết phiên
- **Data backup**: Sao lưu dữ liệu
- **Data export**: Xuất dữ liệu
- **Privacy settings**: Cài đặt riêng tư

---

## 12. **🌐 ĐA NGÔN NGỮ (LOCALIZATION)**

### **12.1 Ngôn ngữ hỗ trợ**
- **Tiếng Việt (vi_VN)**: Ngôn ngữ chính
- **Tiếng Anh (en_US)**: Ngôn ngữ quốc tế
- **Tiếng Nhật (ja_JP)**: Mở rộng thị trường

### **12.2 Tính năng đa ngôn ngữ**
- **Dynamic language switching**: Chuyển ngôn ngữ động
- **Complete translation**: Dịch toàn bộ giao diện
- **Number formatting**: Định dạng số theo locale
- **Date formatting**: Định dạng ngày theo locale
- **Currency formatting**: Định dạng tiền tệ theo locale

### **12.3 Font support**
- **International fonts**: Font hỗ trợ ký tự quốc tế
- **Vietnamese fonts**: Font tiếng Việt
- **Japanese fonts**: Font tiếng Nhật
- **Fallback fonts**: Font dự phòng
- **Custom fonts**: Font tùy chỉnh

---

## 13. **📱 RESPONSIVE DESIGN**

### **13.1 Hỗ trợ thiết bị**
- **Mobile phones**: Điện thoại di động
- **Tablets**: Máy tính bảng
- **Desktop**: Máy tính để bàn
- **Web browsers**: Trình duyệt web
- **Cross-platform**: Đa nền tảng

### **13.2 Adaptive UI**
- **Breakpoint system**: Hệ thống breakpoint
- **Flexible layouts**: Layout linh hoạt
- **Responsive grids**: Lưới responsive
- **Adaptive navigation**: Navigation thích ứng
- **Touch-friendly**: Thân thiện với cảm ứng

### **13.3 Mobile optimizations**
- **Hamburger menu**: Menu hamburger trên mobile
- **Swipe gestures**: Cử chỉ vuốt
- **Pull-to-refresh**: Kéo để làm mới
- **Infinite scroll**: Cuộn vô hạn
- **Offline support**: Hỗ trợ offline

---

## 14. **🔒 BẢO MẬT VÀ QUYỀN RIÊNG TƯ**

### **14.1 Xác thực và phân quyền**
- **Supabase Auth**: Xác thực qua Supabase
- **Email/Password**: Đăng nhập bằng email và mật khẩu
- **Remember login**: Tính năng nhớ đăng nhập với local storage
- **Auto user creation**: Tự động tạo user nếu chưa tồn tại
- **Session management**: Quản lý phiên đăng nhập
- **Role system**: Hệ thống vai trò (admin, manager, user)

### **14.2 Bảo mật dữ liệu**
- **Supabase RLS**: Row Level Security của Supabase
- **Multi-tenant isolation**: Cách ly dữ liệu giữa các user
- **Secure storage**: Lưu trữ bảo mật với Hive
- **JWT tokens**: Xác thực bằng JWT tokens
- **HTTPS**: Truyền tải dữ liệu qua HTTPS

### **14.3 Demo mode**
- **Offline demo**: Chế độ demo không cần internet
- **Local data**: Dữ liệu demo được lưu local
- **Fallback mode**: Tự động chuyển demo khi không có mạng

---

## 15. **⚡ HIỆU SUẤT VÀ TỐI ƯU**

### **15.1 Hiệu suất ứng dụng**
- **Fast loading**: Tải nhanh
- **Smooth animations**: Hoạt ảnh mượt
- **Memory optimization**: Tối ưu bộ nhớ
- **Battery optimization**: Tối ưu pin
- **Network optimization**: Tối ưu mạng

### **15.2 Caching và offline**
- **Local caching**: Cache local
- **Offline support**: Hỗ trợ offline
- **Data synchronization**: Đồng bộ dữ liệu
- **Background sync**: Đồng bộ nền
- **Conflict resolution**: Giải quyết xung đột

### **15.3 Database optimization**
- **Query optimization**: Tối ưu truy vấn
- **Indexing**: Đánh chỉ mục
- **Connection pooling**: Pool kết nối
- **Lazy loading**: Tải lazy
- **Pagination**: Phân trang

---

## 16. **🧪 TESTING VÀ QUALITY ASSURANCE**

### **16.1 Testing đã implement**
- **Widget tests**: Test các widget chính như cart, product grid, dashboard
- **Unit tests**: Test các service và provider
- **Mock data**: Dữ liệu test cho các tính năng
- **Flutter analyze**: Kiểm tra code quality
- **Test coverage**: Một số test cơ bản đã có

### **16.2 Error handling**
- **Try-catch blocks**: Xử lý exception trong services
- **Error states**: Hiển thị lỗi trong UI
- **Loading states**: Trạng thái loading khi xử lý
- **Fallback data**: Dữ liệu dự phòng khi lỗi
- **User feedback**: Snackbar thông báo lỗi/thành công

---

## 17. **🚀 DEPLOYMENT VÀ ENVIRONMENT**

### **17.1 Environment setup**
- **Flutter SDK**: 3.32.0 (locked version)
- **Environment variables**: .env file cho cấu hình
- **Supabase config**: Cấu hình kết nối Supabase
- **Demo mode**: Chế độ demo không cần backend

### **17.2 Build support**
- **Multi-platform**: Android, iOS, Web, Desktop
- **Responsive design**: Hỗ trợ nhiều kích thước màn hình
- **Asset management**: Quản lý fonts, images, icons
- **Localization**: Hỗ trợ đa ngôn ngữ

---

---

## � **FLOW TỔNG THỂ HỆ THỐNG**

### **Luồng nghiệp vụ chính**:

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   LOGIN     │───▶│  DASHBOARD  │───▶│ SETUP DATA  │
│             │    │             │    │             │
│ • Auth user │    │ • Overview  │    │ • Products  │
│ • Load data │    │ • Quick nav │    │ • Categories│
└─────────────┘    └─────────────┘    │ • Partners  │
                                      │ • Printers  │
                                      └─────────────┘
                                             │
                   ┌─────────────────────────┘
                   │
                   ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│    POS      │───▶│   PAYMENT   │───▶│   INVOICE   │
│             │    │             │    │             │
│ • Select    │    │ • Process   │    │ • Display   │
│   products  │    │   payment   │    │ • Print     │
│ • Build     │    │ • Validate  │    │ • Share     │
│   cart      │    │   stock     │    │             │
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │
       │                   ▼                   │
       │           ┌─────────────┐             │
       │           │  INVENTORY  │             │
       │           │   UPDATE    │             │
       │           │             │             │
       │           │ • Deduct    │             │
       │           │   stock     │             │
       │           │ • Update    │             │
       │           │   products  │             │
       │           └─────────────┘             │
       │                   │                   │
       │                   ▼                   │
       │           ┌─────────────┐             │
       │           │NOTIFICATION │             │
       │           │             │             │
       │           │ • Order     │             │
       │           │   created   │             │
       │           │ • Low stock │             │
       │           │   alert     │             │
       │           └─────────────┘             │
       │                                       │
       └───────────────────┬───────────────────┘
                           │
                           ▼
                   ┌─────────────┐
                   │   REPORTS   │
                   │             │
                   │ • Sales     │
                   │ • Inventory │
                   │ • Financial │
                   └─────────────┘
```

---

## ⚖️ **RÀNG BUỘC VÀ QUY TẮC NGHIỆP VỤ**

### **Ràng buộc dữ liệu**:
- **User isolation**: Mỗi user chỉ thấy dữ liệu của mình (RLS)
- **Stock validation**: Không được bán quá số lượng tồn kho
- **Price validation**: Giá bán và giá vốn phải > 0
- **Required fields**: Tên sản phẩm, tên đối tác là bắt buộc
- **Unique constraints**: SKU, barcode phải unique trong hệ thống

### **Ràng buộc nghiệp vụ**:
- **Order completion**: Chỉ order completed mới được tính doanh thu
- **Stock auto-update**: Tự động trừ tồn kho khi hoàn thành đơn hàng
- **Notification trigger**: Tự động gửi thông báo khi có sự kiện
- **Invoice generation**: Chỉ tạo hóa đơn từ completed orders
- **Credit limit**: Không được vượt quá hạn mức tín dụng

### **Ràng buộc kỹ thuật**:
- **Network dependency**: Cần internet cho Supabase (trừ demo mode)
- **Printer connectivity**: Máy in phải cùng mạng LAN
- **Image storage**: Hình ảnh lưu trên Supabase Storage
- **Real-time sync**: Dữ liệu đồng bộ real-time giữa devices
- **Responsive design**: Phải hoạt động tốt trên mọi kích thước màn hình

---

## 🎯 **KẾT LUẬN**

**City POS** là hệ thống quản lý bán hàng tích hợp hoàn chỉnh với các tính năng:

### **✅ Đã hoàn thành**:
- **Core POS**: Bán hàng multi-tab với barcode scanner
- **Inventory**: Quản lý sản phẩm, danh mục, xuất nhập kho
- **Partners**: Quản lý khách hàng và nhà cung cấp
- **Finance**: Theo dõi dòng tiền thu/chi
- **Reports**: Báo cáo bán hàng, tồn kho, tài chính
- **Invoices**: Hiển thị và in hóa đơn
- **Notifications**: Thông báo in-app và push
- **Printing**: In hóa đơn qua máy in mạng
- **Multi-language**: Hỗ trợ VI/EN/JP
- **Responsive**: Mobile/Tablet/Desktop

### **🔗 Điểm mạnh về tích hợp**:
- **Luồng nghiệp vụ liền mạch**: POS → Inventory → Finance → Reports
- **Real-time updates**: Dữ liệu cập nhật ngay lập tức
- **Auto-triggers**: Tự động xử lý inventory, notifications
- **Multi-tenant**: Cách ly dữ liệu an toàn
- **Offline support**: Demo mode không cần internet

Hệ thống đã sẵn sàng triển khai cho các doanh nghiệp nhỏ và vừa với đầy đủ tính năng cần thiết cho việc quản lý bán hàng hiệu quả.
