# ĐẶC TẢ TÍNH NĂNG HỆ THỐNG CITY POS

## 📋 **TỔNG QUAN HỆ THỐNG**

**City POS** là hệ thống quản lý bán hàng tích hợp đầy đủ, bao gồm: <PERSON><PERSON> b<PERSON>, quản lý tồn kho, xu<PERSON><PERSON> nh<PERSON><PERSON> kho, quản lý đối tác, báo cáo và in hóa đơn.

**Công nghệ**: Flutter + Supabase + Riverpod + Hive + Local Notifications
**Nền tảng**: Mobile (Android/iOS), Tablet (Desktop/Web planned)
**Ngôn ngữ**: Tiế<PERSON> V<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Tiếng Nhật (.arb files)

---

## 🔗 **QUAN HỆ GIỮA CÁC MODULE**

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   AUTHENTICATION│    │    DASHBOARD    │    │   SETTINGS      │
│                 │    │                 │    │                 │
│ • Đăng nhập     │────│ • Thống kê      │────│ • Cài đặt app   │
│ • Phân quyền    │    │ • Quick actions │    │ • Cài đặt máy in│
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         │              │   NOTIFICATIONS │              │
         │              │                 │              │
         └──────────────│ • Thông báo     │──────────────┘
                        │ • Push alerts   │
                        └─────────────────┘
                                 │
    ┌─────────────────┬──────────┼──────────┬─────────────────┐
    │                 │          │          │                 │
┌───▼───┐    ┌────────▼──┐   ┌───▼────┐   ┌▼─────────┐   ┌───▼────┐
│  POS  │    │INVENTORY  │   │FINANCE │   │PARTNERS  │   │REPORTS │
│       │    │           │   │        │   │          │   │        │
│ Bán   │◄──►│ Sản phẩm  │   │ Thu/Chi│   │ KH & NCC │   │ Báo cáo│
│ hàng  │    │ Tồn kho   │   │ Sổ quỹ │   │ Công nợ  │   │ Thống kê│
└───┬───┘    │ Xuất/Nhập │   └────────┘   └──────────┘   └────────┘
    │        └───────────┘
    │
┌───▼────┐
│INVOICES│
│        │
│ Hóa đơn│
│ In ấn  │
└────────┘
```

---

## 📱 **CÁC TÍNH NĂNG CHI TIẾT**

## 1. **🔐 AUTHENTICATION - HỆ THỐNG XÁC THỰC**

### **Mục đích**: Quản lý truy cập và bảo mật hệ thống

### **Tính năng chính**:
- **Đăng nhập**: Email + Password qua Supabase Auth
- **Đăng ký**: Tạo tài khoản mới với thông tin cơ bản
- **Remember Login**: Lưu thông tin đăng nhập local (SecureStorage)
- **Auto User Creation**: Tự động tạo user nếu chưa tồn tại
- **Demo Mode**: Chế độ offline với dữ liệu demo cố định

### **Flow đăng nhập**:
```
1. User nhập email/password
2. Kiểm tra "Remember Login" checkbox
3. Gọi AuthService.signIn()
4. Nếu user không tồn tại → Tự động tạo user mới
5. Lưu credentials nếu "Remember Login" = true
6. Chuyển đến Dashboard
```

### **Ràng buộc**:
- **Email**: Phải đúng định dạng email
- **Password**: Tối thiểu 6 ký tự
- **Session**: Tự động expire theo Supabase config
- **Multi-tenant**: Mỗi user chỉ thấy dữ liệu của mình (RLS)

### **Quan hệ với module khác**:
- **→ Dashboard**: Sau khi đăng nhập thành công
- **→ All Modules**: Cung cấp user context cho tất cả tính năng
- **→ Notifications**: User ID để gửi thông báo cá nhân

---

## 2. **📊 DASHBOARD - TRANG CHÍNH**

### **Mục đích**: Tổng quan tình hình kinh doanh và điều hướng nhanh

### **Thống kê hiển thị**:
- **Doanh thu hôm nay**: Tổng tiền từ orders có status = 'completed'
- **Số đơn hàng hôm nay**: Đếm orders trong ngày hiện tại
- **Sản phẩm sắp hết**: Products có stock_quantity < min_stock_level
- **Tổng khách hàng**: Partners có type = 'customer' và is_active = true

### **Quick Actions**:
- **Bán hàng**: → POS Screen
- **Thêm sản phẩm**: → Add Product Screen
- **Xuất nhập kho**: → Stock Transactions Screen
- **Xem báo cáo**: → Reports Screen

### **Flow hoạt động**:
```
1. Load dashboard data từ DashboardService
2. Hiển thị 4 thống kê chính (responsive grid)
3. Auto-refresh mỗi khi navigate back từ screen khác
4. Pull-to-refresh để cập nhật thủ công
5. Click Quick Action → Navigate đến screen tương ứng
```

### **Ràng buộc**:
- **Real-time**: Dữ liệu phải cập nhật khi có thay đổi
- **Performance**: Load nhanh với parallel queries
- **Responsive**: Hiển thị khác nhau trên mobile/desktop

### **Quan hệ với module khác**:
- **← Orders**: Lấy dữ liệu doanh thu và số đơn hàng
- **← Products**: Lấy thông tin sản phẩm sắp hết hàng
- **← Partners**: Đếm số lượng khách hàng
- **→ All Modules**: Điều hướng đến các tính năng khác

---

## 3. **🛒 POS - HỆ THỐNG BÁN HÀNG**

### **Mục đích**: Xử lý giao dịch bán hàng từ A-Z

### **Tính năng chính**:
- **Multi-tab POS**: Nhiều đơn hàng cùng lúc với POSTabData và persistence
- **Product selection**: Grid sản phẩm với category filter và search
- **Barcode scanner**: Quét mã vạch với MobileScanner (camera)
- **Cart management**: Thêm/xóa/điều chỉnh số lượng với auto-save
- **Payment processing**: Thanh toán tiền mặt với validation
- **Invoice generation**: Tạo PDF và hiển thị hóa đơn

### **Flow bán hàng**:
```
1. Chọn sản phẩm từ grid hoặc scan barcode
2. Sản phẩm được thêm vào cart với quantity = 1
3. Điều chỉnh quantity hoặc xóa sản phẩm nếu cần
4. Nhập thông tin khách hàng (optional)
5. Chọn payment method và nhập số tiền
6. Validate: Kiểm tra stock availability
7. Tạo Order → Trừ inventory → Tạo notification
8. Hiển thị invoice → Option in hóa đơn
9. Clear cart và tạo tab mới
```

### **Ràng buộc nghiệp vụ**:
- **Stock validation**: Phải đủ hàng mới được bán
- **Payment validation**: Chỉ hỗ trợ thanh toán tiền mặt
- **Auto inventory update**: Tự động trừ tồn kho khi hoàn thành
- **Order numbering**: Tự động tạo mã đơn duy nhất
- **Cart persistence**: Lưu tất cả tabs với POSPersistenceService

### **Quan hệ với module khác**:
- **← Products**: Lấy danh sách sản phẩm để bán
- **← Categories**: Filter sản phẩm theo danh mục
- **→ Orders**: Tạo đơn hàng mới
- **→ Inventory**: Trừ tồn kho tự động
- **→ Invoices**: Tạo hóa đơn
- **→ Notifications**: Thông báo đơn hàng mới
- **→ Finance**: Ghi nhận doanh thu (gián tiếp)

---

## 4. **📦 INVENTORY - QUẢN LÝ TỒN KHO**

### **Mục đích**: Quản lý sản phẩm, danh mục và xuất nhập kho

### **4.1 Quản lý sản phẩm**
- **Product CRUD**: Thêm/sửa/xóa/xem sản phẩm với ProductService
- **Product info**: Tên, mô tả, SKU, barcode, giá bán, giá vốn, đơn vị
- **Stock management**: Tồn kho hiện tại, tối thiểu, tối đa
- **Image upload**: Upload hình ảnh qua SupabaseStorageService với tối ưu hóa
- **Category assignment**: Gán sản phẩm vào danh mục với CategoryService

### **4.2 Quản lý danh mục**
- **Category CRUD**: Thêm/sửa/xóa danh mục
- **Visual identity**: Màu sắc và icon cho danh mục
- **Product filtering**: Lọc sản phẩm theo danh mục

### **4.3 Xuất nhập kho (Stock Transactions)**
- **Transaction types**: Import (nhập), Export (xuất) với StockTransactionType
- **Multi-product transactions**: Nhiều sản phẩm trong một phiếu
- **Cost tracking**: Giá vốn cho từng item với StockTransactionItem
- **Transaction history**: Lịch sử với StockTransactionService
- **Auto stock update**: Tự động cập nhật stock_quantity

### **Flow xuất nhập kho**:
```
1. Vào Stock Transactions Screen
2. Click "Thêm giao dịch" → Add Stock Transaction Screen
3. Chọn loại: Import hoặc Export
4. Thêm sản phẩm: Chọn product + quantity + unit_price
5. Nhập ghi chú (optional)
6. Save → Tạo StockTransaction + StockTransactionItems
7. Cập nhật stock_quantity của products
8. Gửi notification về thay đổi tồn kho
```

### **Ràng buộc**:
- **Stock validation**: Export không được vượt quá tồn kho hiện tại
- **Price validation**: Unit price phải > 0
- **Quantity validation**: Quantity phải > 0
- **Auto stock update**: Tự động cập nhật product.stock_quantity

### **Quan hệ với module khác**:
- **← Categories**: Phân loại sản phẩm
- **→ POS**: Cung cấp sản phẩm để bán
- **→ Dashboard**: Cảnh báo low stock
- **→ Reports**: Dữ liệu cho báo cáo tồn kho
- **← Orders**: Tự động trừ tồn kho khi bán

---

## 5. **💰 FINANCE - QUẢN LÝ TÀI CHÍNH**

### **Mục đích**: Theo dõi dòng tiền thu/chi và tình hình tài chính

### **Tính năng chính**:
- **Finance transactions**: Quản lý giao dịch thu/chi với FinanceService
- **Cash flow tracking**: Theo dõi dòng tiền với CashFlowChart
- **Auto receipt creation**: Tự động tạo phiếu thu từ đơn hàng
- **Financial reports**: Báo cáo tài chính với biểu đồ

### **Flow quản lý tài chính**:
```
1. Vào Finance Screen → Hiển thị danh sách giao dịch
2. Add Transaction → Chọn loại (Thu/Chi)
3. Nhập: Số tiền, Danh mục, Ghi chú, Ngày
4. Save → Tạo finance transaction
5. Auto update balance và cash flow
6. Hiển thị trong reports
```

### **Ràng buộc**:
- **Amount validation**: Số tiền phải > 0
- **Category required**: Phải chọn danh mục giao dịch
- **Date validation**: Ngày không được trong tương lai
- **Balance tracking**: Tự động tính số dư

### **Quan hệ với module khác**:
- **← POS**: Tự động ghi nhận doanh thu từ bán hàng
- **→ Reports**: Cung cấp dữ liệu cho báo cáo tài chính
- **→ Dashboard**: Hiển thị doanh thu hôm nay

---

## 6. **🤝 PARTNERS - QUẢN LÝ ĐỐI TÁC**

### **Mục đích**: Quản lý thông tin khách hàng và nhà cung cấp

### **Tính năng chính**:
- **Partner CRUD**: Thêm/sửa/xóa/xem đối tác
- **Partner types**: Customer, Supplier, Both
- **Contact management**: Thông tin liên lạc đầy đủ
- **Credit management**: Hạn mức tín dụng và công nợ
- **Search & filter**: Tìm kiếm theo tên, loại, trạng thái

### **Thông tin đối tác**:
- **Basic info**: Tên, email, phone, địa chỉ
- **Business info**: Mã số thuế, loại đối tác
- **Financial info**: Hạn mức tín dụng, số dư hiện tại
- **Status**: Trạng thái hoạt động

### **Flow quản lý đối tác**:
```
1. Vào Partners Screen → Danh sách đối tác
2. Add Partner → Nhập thông tin cơ bản
3. Chọn type: Customer/Supplier/Both
4. Set credit limit (nếu cần)
5. Save → Tạo partner record
6. Có thể edit/deactivate sau này
```

### **Ràng buộc**:
- **Name required**: Tên đối tác bắt buộc
- **Email format**: Email phải đúng định dạng
- **Credit limit**: Phải >= 0
- **Type validation**: Phải chọn một trong 3 loại

### **Quan hệ với module khác**:
- **→ POS**: Chọn khách hàng khi bán hàng
- **→ Orders**: Liên kết đơn hàng với khách hàng
- **→ Dashboard**: Đếm tổng số khách hàng
- **→ Reports**: Báo cáo theo đối tác

---

## 7. **📋 INVOICES - QUẢN LÝ HÓA ĐƠN**

### **Mục đích**: Hiển thị và quản lý hóa đơn bán hàng

### **Tính năng chính**:
- **Invoice display**: Hiển thị hóa đơn sau thanh toán POS
- **PDF generation**: Tạo file PDF với PDFService và font fallback
- **Print support**: In hóa đơn qua máy in mạng với PrinterService
- **Share functionality**: Chia sẻ hóa đơn PDF với Share package
- **Auto generation**: Tự động tạo từ completed orders

### **Flow hóa đơn**:
```
1. POS hoàn thành thanh toán → Tự động tạo Order
2. Nếu setting "Show invoice after payment" = true
   → Navigate to Invoice Display Screen
3. Hiển thị thông tin đầy đủ: Order details, items, customer
4. User có thể: In hóa đơn, Share PDF, Back to POS
5. Invoice được lưu trong Invoices Screen để xem lại
```

### **Thông tin hóa đơn**:
- **Order information**: Mã đơn, ngày, tổng tiền
- **Customer details**: Tên, phone khách hàng (nếu có)
- **Items list**: Danh sách sản phẩm, số lượng, giá
- **Payment info**: Phương thức thanh toán, tiền thừa
- **Company info**: Logo, tên, địa chỉ công ty

### **Ràng buộc**:
- **Auto generation**: Chỉ tạo từ completed orders
- **Print validation**: Kiểm tra máy in trước khi in
- **PDF format**: Định dạng chuẩn cho hóa đơn

### **Quan hệ với module khác**:
- **← POS**: Nhận order data để tạo hóa đơn
- **← Settings**: Cài đặt hiển thị và in hóa đơn
- **← Printing**: Sử dụng printer service để in

---

## 8. **📊 REPORTS - BÁO CÁO**

### **Mục đích**: Cung cấp thông tin phân tích kinh doanh

### **Loại báo cáo**:
- **Sales Report**: Doanh thu theo thời gian với fl_chart
- **Inventory Report**: Tình hình tồn kho và giá trị
- **Financial Report**: Cash flow với CashFlowChart
- **Dashboard Stats**: Thống kê tổng quan với DashboardService

### **Tính năng báo cáo**:
- **Date range filter**: Chọn khoảng thời gian báo cáo
- **Chart visualization**: Biểu đồ với fl_chart
- **Data tables**: Bảng dữ liệu chi tiết
- **Responsive design**: Hiển thị tối ưu trên mọi thiết bị

### **Flow báo cáo**:
```
1. Vào Reports Screen → Chọn loại báo cáo
2. Set date range (start date, end date)
3. Load data từ ReportsService
4. Hiển thị chart + data table
5. User có thể thay đổi period (daily/weekly/monthly)
```

### **Nguồn dữ liệu**:
- **Sales data**: Từ completed orders
- **Inventory data**: Từ products và stock transactions
- **Financial data**: Từ finance transactions
- **Demo mode**: Sử dụng generated demo data

### **Quan hệ với module khác**:
- **← Orders**: Dữ liệu doanh thu và bán hàng
- **← Products**: Thông tin sản phẩm và tồn kho
- **← Finance**: Dữ liệu tài chính
- **← Stock Transactions**: Lịch sử xuất nhập kho

---

## 9. **🔔 NOTIFICATIONS - HỆ THỐNG THÔNG BÁO**

### **Mục đích**: Thông báo sự kiện quan trọng cho người dùng

### **Loại thông báo**:
- **Order notifications**: Đơn hàng mới với NotificationService
- **Stock alerts**: Sản phẩm sắp hết hàng với notifyLowStock
- **Payment notifications**: Thanh toán thành công với push notification
- **Stock transaction notifications**: Xuất nhập kho thành công

### **Cơ chế thông báo**:
- **In-app notifications**: Hiển thị trong app với AppNotification model
- **Push notifications**: Local notifications với flutter_local_notifications
- **Auto trigger**: Tự động khi có sự kiện với PushNotificationService
- **Notification settings**: Cài đặt bật/tắt từng loại thông báo

### **Flow thông báo**:
```
1. Sự kiện xảy ra (order created, low stock, etc.)
2. NotificationService.createNotification()
3. Lưu vào database với user_id
4. Gửi push notification (nếu enabled)
5. Update notification badge trong app
6. User click → Mark as read
```

### **Cài đặt thông báo**:
- **Notification preferences**: Bật/tắt từng loại
- **Push notifications**: Bật/tắt push notifications
- **Email notifications**: Bật/tắt email notifications

### **Quan hệ với module khác**:
- **← POS**: Thông báo đơn hàng mới
- **← Inventory**: Cảnh báo low stock
- **← Finance**: Thông báo giao dịch tài chính
- **← System**: Thông báo lỗi và cập nhật

---

## 10. **🖨️ PRINTING - HỆ THỐNG IN ẤN**

### **Mục đích**: In hóa đơn qua máy in mạng

### **Tính năng chính**:
- **Printer management**: Quản lý máy in với Printer model và Hive storage
- **Network printing**: In qua IP/Port với printing package
- **Invoice printing**: In hóa đơn PDF với PrinterService
- **Connection testing**: Test kết nối máy in trước khi lưu

### **Flow thiết lập máy in**:
```
1. Settings → Thiết lập máy in
2. Add Printer → Nhập IP, Port, Tên máy in
3. Test connection → Kiểm tra kết nối
4. Set as default → Đặt làm máy in mặc định
5. Save printer configuration
```

### **Flow in hóa đơn**:
```
1. POS hoàn thành thanh toán → Invoice Display
2. Click "In hóa đơn" button
3. Kiểm tra default printer có tồn tại
4. Generate PDF từ order data
5. Send to printer qua network
6. Hiển thị kết quả (success/error)
```

### **Cấu hình máy in**:
- **Printer info**: Tên, IP address, Port (default 9100)
- **Connection**: Network printer qua TCP/IP
- **Status**: Active/Inactive, Default printer
- **Validation**: Test connection trước khi lưu

### **Quan hệ với module khác**:
- **← Settings**: Cài đặt và quản lý máy in
- **← Invoices**: Nhận lệnh in hóa đơn
- **← PDF Service**: Tạo PDF để in

---

## 11. **⚙️ CÀI ĐẶT HỆ THỐNG (SETTINGS)**

### **11.1 Cài đặt ứng dụng**
- **Language selection**: Chọn ngôn ngữ (VI/EN/JP) với LanguageProvider
- **Theme settings**: Cài đặt giao diện với AppTheme
- **App information**: Thông tin ứng dụng và version
- **User profile**: Thông tin người dùng
- **Logout**: Đăng xuất khỏi hệ thống

### **11.2 Cài đặt hóa đơn**
- **Auto-show invoice**: Tự động hiển thị hóa đơn sau thanh toán
- **Company information**: Thông tin công ty trong PDF
- **Invoice format**: Định dạng hóa đơn PDF
- **Font support**: Hỗ trợ font tiếng Việt trong PDF
- **PDF optimization**: Tối ưu hóa file PDF

### **11.3 Cài đặt máy in**
- **Printer setup**: Thiết lập máy in với IP/Port
- **Default printer**: Chọn máy in mặc định
- **Connection test**: Test kết nối máy in
- **Printer management**: Thêm/sửa/xóa máy in
- **Network printing**: In qua mạng LAN/WiFi

### **11.4 Cài đặt thông báo**
- **Notification preferences**: Bật/tắt từng loại thông báo
- **Push notifications**: Cài đặt push notifications
- **Sound settings**: Cài đặt âm thanh thông báo
- **Badge settings**: Hiển thị badge số lượng thông báo
- **Auto notifications**: Tự động gửi thông báo khi có sự kiện

---

## 12. **🌐 ĐA NGÔN NGỮ (LOCALIZATION)**

### **12.1 Ngôn ngữ hỗ trợ**
- **Tiếng Việt (vi)**: Ngôn ngữ chính với app_vi.arb
- **Tiếng Anh (en)**: Ngôn ngữ quốc tế với app_en.arb
- **Tiếng Nhật (ja)**: Mở rộng thị trường với app_ja.arb

### **12.2 Tính năng đa ngôn ngữ**
- **Dynamic language switching**: Chuyển ngôn ngữ với LanguageProvider
- **Complete translation**: Dịch toàn bộ giao diện với AppLocalizations
- **ARB files**: Sử dụng .arb files cho translation
- **Intl package**: Định dạng số và ngày với intl package
- **Runtime switching**: Chuyển ngôn ngữ không cần restart app

### **12.3 Font support**
- **PDF fonts**: Font hỗ trợ tiếng Việt trong PDF với font fallback
- **UI fonts**: Font system mặc định cho giao diện
- **International support**: Hỗ trợ ký tự đặc biệt
- **Font loading**: Load font async với error handling
- **Fallback system**: Hệ thống font dự phòng khi load lỗi

---

## 13. **📱 RESPONSIVE DESIGN**

### **13.1 Hỗ trợ thiết bị**
- **Mobile phones**: Android và iOS (chính)
- **Tablets**: Hỗ trợ tablet layout
- **Desktop**: Planned (chưa implement)
- **Web browsers**: Planned (chưa implement)
- **Cross-platform**: Flutter framework

### **13.2 Adaptive UI**
- **Breakpoint system**: Hệ thống breakpoint
- **Flexible layouts**: Layout linh hoạt
- **Responsive grids**: Lưới responsive
- **Adaptive navigation**: Navigation thích ứng
- **Touch-friendly**: Thân thiện với cảm ứng

### **13.3 Mobile optimizations**
- **Hamburger menu**: Menu hamburger trên mobile
- **Swipe gestures**: Cử chỉ vuốt
- **Pull-to-refresh**: Kéo để làm mới
- **Infinite scroll**: Cuộn vô hạn
- **Offline support**: Hỗ trợ offline

---



---

## 🔄 **FLOW TỔNG THỂ HỆ THỐNG**

### **Luồng nghiệp vụ chính**:

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   LOGIN     │───▶│  DASHBOARD  │───▶│ SETUP DATA  │
│             │    │             │    │             │
│ • Auth user │    │ • Overview  │    │ • Products  │
│ • Load data │    │ • Quick nav │    │ • Categories│
└─────────────┘    └─────────────┘    │ • Partners  │
                                      │ • Printers  │
                                      └─────────────┘
                                             │
                   ┌─────────────────────────┘
                   │
                   ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│    POS      │───▶│   PAYMENT   │───▶│   INVOICE   │
│             │    │             │    │             │
│ • Select    │    │ • Process   │    │ • Display   │
│   products  │    │   payment   │    │ • Print     │
│ • Build     │    │ • Validate  │    │ • Share     │
│   cart      │    │   stock     │    │             │
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │
       │                   ▼                   │
       │           ┌─────────────┐             │
       │           │  INVENTORY  │             │
       │           │   UPDATE    │             │
       │           │             │             │
       │           │ • Deduct    │             │
       │           │   stock     │             │
       │           │ • Update    │             │
       │           │   products  │             │
       │           └─────────────┘             │
       │                   │                   │
       │                   ▼                   │
       │           ┌─────────────┐             │
       │           │NOTIFICATION │             │
       │           │             │             │
       │           │ • Order     │             │
       │           │   created   │             │
       │           │ • Low stock │             │
       │           │   alert     │             │
       │           └─────────────┘             │
       │                                       │
       └───────────────────┬───────────────────┘
                           │
                           ▼
                   ┌─────────────┐
                   │   REPORTS   │
                   │             │
                   │ • Sales     │
                   │ • Inventory │
                   │ • Financial │
                   └─────────────┘
```

---

## ⚖️ **RÀNG BUỘC VÀ QUY TẮC NGHIỆP VỤ**

### **Ràng buộc dữ liệu**:
- **User isolation**: Mỗi user chỉ thấy dữ liệu của mình (RLS)
- **Stock validation**: Không được bán quá số lượng tồn kho
- **Price validation**: Giá bán và giá vốn phải > 0
- **Required fields**: Tên sản phẩm, tên đối tác là bắt buộc
- **Unique constraints**: SKU, barcode phải unique trong hệ thống

### **Ràng buộc nghiệp vụ**:
- **Order completion**: Chỉ order completed mới được tính doanh thu
- **Stock auto-update**: Tự động trừ tồn kho khi hoàn thành đơn hàng
- **Notification trigger**: Tự động gửi thông báo khi có sự kiện
- **Invoice generation**: Chỉ tạo hóa đơn từ completed orders
- **Credit limit**: Không được vượt quá hạn mức tín dụng

### **Ràng buộc kỹ thuật**:
- **Network dependency**: Cần internet cho Supabase (trừ demo mode)
- **Printer connectivity**: Máy in phải cùng mạng LAN
- **Image storage**: Hình ảnh lưu trên Supabase Storage
- **Real-time sync**: Dữ liệu đồng bộ real-time giữa devices
- **Responsive design**: Phải hoạt động tốt trên mọi kích thước màn hình

---

## 🎯 **KẾT LUẬN**

**City POS** là hệ thống quản lý bán hàng tích hợp hoàn chỉnh với các tính năng:

### **✅ Đã hoàn thành**:
- **Core POS**: Bán hàng multi-tab với barcode scanner (MobileScanner)
- **Inventory**: Quản lý sản phẩm, danh mục, xuất nhập kho với Supabase
- **Partners**: Quản lý khách hàng và nhà cung cấp
- **Finance**: Theo dõi dòng tiền thu/chi với auto receipt creation
- **Reports**: Dashboard stats và cash flow chart với fl_chart
- **Invoices**: PDF generation và share với font fallback
- **Notifications**: Local push notifications với flutter_local_notifications
- **Printing**: In hóa đơn qua network printer với printing package
- **Multi-language**: Hỗ trợ VI/EN/JP với .arb files
- **Authentication**: Supabase Auth với demo mode và auto user creation

### **⚠️ Hạn chế hiện tại**:
- **Payment methods**: Chỉ hỗ trợ thanh toán tiền mặt
- **Platform support**: Chỉ Mobile/Tablet, chưa có Desktop/Web
- **Push notifications**: Chỉ local notifications, chưa có FCM
- **Invoice management**: Chỉ có display và print, chưa có danh sách quản lý
- **Financial reports**: Chỉ có cash flow chart, chưa có báo cáo chi tiết
- **Demo mode**: Dữ liệu cố định, không có auto fallback khi mất mạng

### **🔗 Điểm mạnh về tích hợp**:
- **Luồng nghiệp vụ liền mạch**: POS → Inventory → Finance → Notifications
- **Auto-triggers**: Tự động trừ tồn kho, tạo phiếu thu, gửi thông báo
- **Multi-tenant**: RLS policies đảm bảo cách ly dữ liệu
- **State management**: Riverpod với providers tối ưu
- **Local storage**: Hive cho printer settings và cart persistence

### **🎯 Phù hợp cho**:
- **Cửa hàng nhỏ**: Bán lẻ với nhu cầu POS cơ bản
- **Quán ăn/Café**: Thanh toán nhanh với barcode scanner
- **Cửa hàng tạp hóa**: Quản lý tồn kho và xuất nhập đơn giản
- **Doanh nghiệp vừa**: Cần báo cáo tài chính cơ bản

Hệ thống đã có đủ tính năng cốt lõi cho việc quản lý bán hàng, phù hợp triển khai cho các doanh nghiệp nhỏ và vừa với yêu cầu không quá phức tạp.
